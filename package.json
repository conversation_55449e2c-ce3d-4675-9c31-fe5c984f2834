{"name": "rimac-medical-appointments-backend", "version": "1.0.0", "description": "Backend para agendamiento de citas médicas - <PERSON><PERSON><PERSON>", "main": "handler.js", "scripts": {"build": "tsc", "dev": "sls offline start", "deploy": "sls deploy", "deploy:dev": "sls deploy --stage dev", "deploy:prod": "sls deploy --stage prod", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "docs": "swagger-jsdoc -d swaggerDef.js src/**/*.ts -o swagger.json"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.450.0", "@aws-sdk/client-eventbridge": "^3.450.0", "@aws-sdk/client-sns": "^3.450.0", "@aws-sdk/client-sqs": "^3.450.0", "@aws-sdk/lib-dynamodb": "^3.450.0", "aws-lambda": "^1.0.7", "joi": "^17.11.0", "mysql2": "^3.6.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.126", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "serverless": "^3.38.0", "serverless-dotenv-plugin": "^6.0.0", "serverless-offline": "^13.3.0", "serverless-plugin-typescript": "^2.1.4", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "author": "<PERSON><PERSON><PERSON> - Medical Appointments Team", "license": "MIT", "keywords": ["serverless", "aws", "lambda", "dynamodb", "mysql", "medical-appointments", "rimac"]}