/**
 * EventBridge Service - Handles Amazon EventBridge operations
 * Publishes appointment processing events to EventBridge custom bus
 */

import { EventBridgeClient, PutEventsCommand } from '@aws-sdk/client-eventbridge';
import { AppointmentEventBridgeEventDTO } from '@/entities/DTOs';
import { IEventBridgeService, ILogger } from '@/entities/interfaces';

export class EventBridgeService implements IEventBridgeService {
  private eventBridgeClient: EventBridgeClient;
  private eventBusName: string;
  private logger: ILogger;

  constructor(eventBridgeClient: EventBridgeClient, eventBusName: string, logger: ILogger) {
    this.eventBridgeClient = eventBridgeClient;
    this.eventBusName = eventBusName;
    this.logger = logger;
  }

  /**
   * Publishes an appointment processing event to EventBridge
   */
  public async publishEvent(event: AppointmentEventBridgeEventDTO): Promise<string> {
    try {
      this.logger.info('Publishing event to EventBridge', {
        appointmentId: event.detail.appointmentId,
        countryISO: event.detail.countryISO,
        status: event.detail.status,
        eventBusName: this.eventBusName,
      });

      // Validate event
      this.validateEvent(event);

      // Create put events command
      const command = new PutEventsCommand({
        Entries: [
          {
            Source: event.source,
            DetailType: event['detail-type'],
            Detail: JSON.stringify(event.detail),
            EventBusName: this.eventBusName,
            Time: new Date(),
            Resources: [
              `appointment:${event.detail.appointmentId}`,
              `country:${event.detail.countryISO}`,
            ],
          },
        ],
      });

      // Publish event
      const result = await this.eventBridgeClient.send(command);

      // Check for failures
      if (result.FailedEntryCount && result.FailedEntryCount > 0) {
        const failures = result.Entries?.filter(entry => entry.ErrorCode) || [];
        const errorMessages = failures.map(failure => 
          `${failure.ErrorCode}: ${failure.ErrorMessage}`
        ).join(', ');
        throw new Error(`EventBridge publish failed: ${errorMessages}`);
      }

      const eventId = result.Entries?.[0]?.EventId;
      if (!eventId) {
        throw new Error('Failed to get EventId from EventBridge publish result');
      }

      this.logger.info('Event published successfully to EventBridge', {
        appointmentId: event.detail.appointmentId,
        countryISO: event.detail.countryISO,
        eventId,
      });

      return eventId;
    } catch (error) {
      this.logger.error('Error publishing event to EventBridge', error as Error, {
        appointmentId: event.detail.appointmentId,
        countryISO: event.detail.countryISO,
        eventBusName: this.eventBusName,
      });
      throw new Error(`Failed to publish EventBridge event: ${(error as Error).message}`);
    }
  }

  /**
   * Publishes a successful appointment processing event
   */
  public async publishSuccessEvent(
    appointmentId: string,
    insuredId: string,
    scheduleId: number,
    countryISO: 'PE' | 'CL'
  ): Promise<string> {
    const event: AppointmentEventBridgeEventDTO = {
      source: 'rimac.appointments',
      'detail-type': 'Appointment Processed',
      detail: {
        appointmentId,
        insuredId,
        scheduleId,
        countryISO,
        status: 'success',
        processedAt: new Date().toISOString(),
      },
    };

    return this.publishEvent(event);
  }

  /**
   * Publishes a failed appointment processing event
   */
  public async publishFailureEvent(
    appointmentId: string,
    insuredId: string,
    scheduleId: number,
    countryISO: 'PE' | 'CL',
    errorMessage: string
  ): Promise<string> {
    const event: AppointmentEventBridgeEventDTO = {
      source: 'rimac.appointments',
      'detail-type': 'Appointment Processed',
      detail: {
        appointmentId,
        insuredId,
        scheduleId,
        countryISO,
        status: 'failure',
        processedAt: new Date().toISOString(),
        errorMessage,
      },
    };

    return this.publishEvent(event);
  }

  /**
   * Publishes a test event to verify EventBridge connectivity
   */
  public async publishTestEvent(): Promise<string> {
    try {
      this.logger.info('Publishing test event to EventBridge', { eventBusName: this.eventBusName });

      const testEvent: AppointmentEventBridgeEventDTO = {
        source: 'rimac.appointments',
        'detail-type': 'Health Check',
        detail: {
          appointmentId: 'test-appointment-id',
          insuredId: 'test-insured-id',
          scheduleId: 999999,
          countryISO: 'PE',
          status: 'success',
          processedAt: new Date().toISOString(),
        },
      };

      const eventId = await this.publishEvent(testEvent);

      this.logger.info('Test event published successfully to EventBridge', {
        eventId,
      });

      return eventId;
    } catch (error) {
      this.logger.error('Error publishing test event to EventBridge', error as Error, {
        eventBusName: this.eventBusName,
      });
      throw new Error(`Failed to publish EventBridge test event: ${(error as Error).message}`);
    }
  }

  /**
   * Validates EventBridge event before publishing
   */
  private validateEvent(event: AppointmentEventBridgeEventDTO): void {
    if (!event) {
      throw new Error('EventBridge event is required');
    }

    if (!event.source || typeof event.source !== 'string') {
      throw new Error('Valid source is required in EventBridge event');
    }

    if (!event['detail-type'] || typeof event['detail-type'] !== 'string') {
      throw new Error('Valid detail-type is required in EventBridge event');
    }

    if (!event.detail || typeof event.detail !== 'object') {
      throw new Error('Valid detail object is required in EventBridge event');
    }

    // Validate detail properties
    const detail = event.detail;

    if (!detail.appointmentId || typeof detail.appointmentId !== 'string') {
      throw new Error('Valid appointmentId is required in event detail');
    }

    if (!detail.insuredId || typeof detail.insuredId !== 'string') {
      throw new Error('Valid insuredId is required in event detail');
    }

    if (!detail.scheduleId || typeof detail.scheduleId !== 'number' || detail.scheduleId <= 0) {
      throw new Error('Valid scheduleId is required in event detail');
    }

    if (!detail.countryISO || !['PE', 'CL'].includes(detail.countryISO)) {
      throw new Error('Valid countryISO is required in event detail (must be PE or CL)');
    }

    if (!detail.status || !['success', 'failure'].includes(detail.status)) {
      throw new Error('Valid status is required in event detail (must be success or failure)');
    }

    if (!detail.processedAt || typeof detail.processedAt !== 'string') {
      throw new Error('Valid processedAt timestamp is required in event detail');
    }

    // Validate timestamp format (ISO 8601)
    try {
      const date = new Date(detail.processedAt);
      if (isNaN(date.getTime())) {
        throw new Error('Invalid processedAt timestamp format');
      }
    } catch {
      throw new Error('Invalid processedAt timestamp format in event detail');
    }

    // Validate event size (EventBridge limit is 256KB)
    const eventSize = JSON.stringify(event).length;
    if (eventSize > 262144) { // 256KB in bytes
      throw new Error('Event size exceeds EventBridge limit of 256KB');
    }
  }

  /**
   * Health check method to verify EventBridge connectivity
   */
  public async healthCheck(): Promise<boolean> {
    try {
      await this.publishTestEvent();
      return true;
    } catch (error) {
      this.logger.error('EventBridge health check failed', error as Error);
      return false;
    }
  }
}
