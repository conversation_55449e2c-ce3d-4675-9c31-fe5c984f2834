/**
 * Appointment Entity - Represents a medical appointment in the system
 * Used for both DynamoDB and MySQL representations
 */

export type CountryISO = 'PE' | 'CL';
export type AppointmentStatus = 'pending' | 'completed' | 'failed';

/**
 * Base Appointment interface with common properties
 */
export interface BaseAppointment {
  insuredId: string;
  scheduleId: number;
  countryISO: CountryISO;
  status: AppointmentStatus;
}

/**
 * DynamoDB Appointment Entity
 * Stored in DynamoDB with appointmentId as primary key
 */
export interface DynamoDBAppointment extends BaseAppointment {
  appointmentId: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * MySQL Appointment Entity
 * Stored in MySQL RDS with auto-incremental id
 */
export interface MySQLAppointment extends BaseAppointment {
  id?: number;
  date: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Appointment class with business logic
 */
export class Appointment implements DynamoDBAppointment {
  public appointmentId: string;
  public insuredId: string;
  public scheduleId: number;
  public countryISO: CountryISO;
  public status: AppointmentStatus;
  public createdAt: string;
  public updatedAt: string;

  constructor(data: Partial<DynamoDBAppointment>) {
    this.appointmentId = data.appointmentId || '';
    this.insuredId = data.insuredId || '';
    this.scheduleId = data.scheduleId || 0;
    this.countryISO = data.countryISO || 'PE';
    this.status = data.status || 'pending';
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();
  }

  /**
   * Validates if the appointment data is complete and valid
   */
  public isValid(): boolean {
    return !!(
      this.appointmentId &&
      this.insuredId &&
      this.scheduleId > 0 &&
      this.countryISO &&
      ['PE', 'CL'].includes(this.countryISO) &&
      ['pending', 'completed', 'failed'].includes(this.status)
    );
  }

  /**
   * Marks the appointment as completed
   */
  public markAsCompleted(): void {
    this.status = 'completed';
    this.updatedAt = new Date().toISOString();
  }

  /**
   * Marks the appointment as failed
   */
  public markAsFailed(): void {
    this.status = 'failed';
    this.updatedAt = new Date().toISOString();
  }

  /**
   * Converts to MySQL format
   */
  public toMySQLFormat(): MySQLAppointment {
    return {
      insuredId: this.insuredId,
      scheduleId: this.scheduleId,
      countryISO: this.countryISO,
      status: this.status,
      date: new Date(),
    };
  }

  /**
   * Creates an Appointment from DynamoDB item
   */
  public static fromDynamoDB(item: Record<string, unknown>): Appointment {
    return new Appointment({
      appointmentId: item.appointmentId as string,
      insuredId: item.insuredId as string,
      scheduleId: item.scheduleId as number,
      countryISO: item.countryISO as CountryISO,
      status: item.status as AppointmentStatus,
      createdAt: item.createdAt as string,
      updatedAt: item.updatedAt as string,
    });
  }

  /**
   * Converts to DynamoDB format
   */
  public toDynamoDB(): Record<string, unknown> {
    return {
      appointmentId: this.appointmentId,
      insuredId: this.insuredId,
      scheduleId: this.scheduleId,
      countryISO: this.countryISO,
      status: this.status,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
