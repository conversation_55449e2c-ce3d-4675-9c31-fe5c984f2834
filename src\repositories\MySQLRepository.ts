/**
 * MySQL Repository Implementation
 * Implements the Repository Pattern for MySQL operations
 */

import { MySQLAppointment, CountryISO } from '@/entities/Appointment';
import { IMySQLRepository, IMySQLConnection, ILogger } from '@/entities/interfaces';

export class MySQLRepository implements IMySQLRepository {
  private connection: IMySQLConnection;
  private logger: ILogger;

  constructor(connection: IMySQLConnection, logger: ILogger) {
    this.connection = connection;
    this.logger = logger;
  }

  /**
   * Creates a new appointment in MySQL
   */
  public async create(appointment: MySQLAppointment): Promise<MySQLAppointment> {
    try {
      this.logger.info('Creating appointment in MySQL', { 
        insuredId: appointment.insuredId,
        countryISO: appointment.countryISO 
      });

      const sql = `
        INSERT INTO appointments (insuredId, scheduleId, countryISO, status, date, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
      `;

      const params = [
        appointment.insuredId,
        appointment.scheduleId,
        appointment.countryISO,
        appointment.status,
        appointment.date || new Date(),
      ];

      const result = await this.connection.execute(sql, params);

      if (!result.insertId) {
        throw new Error('Failed to get insert ID from MySQL');
      }

      const createdAppointment: MySQLAppointment = {
        ...appointment,
        id: result.insertId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      this.logger.info('Appointment created successfully in MySQL', { 
        id: createdAppointment.id,
        insuredId: appointment.insuredId 
      });

      return createdAppointment;
    } catch (error) {
      this.logger.error('Error creating appointment in MySQL', error as Error, {
        insuredId: appointment.insuredId,
        countryISO: appointment.countryISO
      });
      throw new Error(`Failed to create appointment in MySQL: ${(error as Error).message}`);
    }
  }

  /**
   * Finds an appointment by ID
   */
  public async findById(id: number): Promise<MySQLAppointment | null> {
    try {
      this.logger.debug('Finding appointment by ID in MySQL', { id });

      const sql = `
        SELECT id, insuredId, scheduleId, countryISO, status, date, createdAt, updatedAt
        FROM appointments
        WHERE id = ?
      `;

      const results = await this.connection.query<MySQLAppointment>(sql, [id]);

      if (results.length === 0) {
        this.logger.debug('Appointment not found in MySQL', { id });
        return null;
      }

      const appointment = results[0];
      
      this.logger.debug('Appointment found in MySQL', { 
        id,
        status: appointment.status 
      });

      return appointment;
    } catch (error) {
      this.logger.error('Error finding appointment by ID in MySQL', error as Error, { id });
      throw new Error(`Failed to find appointment in MySQL: ${(error as Error).message}`);
    }
  }

  /**
   * Finds all appointments for a specific insured ID
   */
  public async findByInsuredId(insuredId: string): Promise<MySQLAppointment[]> {
    try {
      this.logger.debug('Finding appointments by insured ID in MySQL', { insuredId });

      const sql = `
        SELECT id, insuredId, scheduleId, countryISO, status, date, createdAt, updatedAt
        FROM appointments
        WHERE insuredId = ?
        ORDER BY createdAt DESC
      `;

      const results = await this.connection.query<MySQLAppointment>(sql, [insuredId]);

      this.logger.debug('Appointments found for insured ID in MySQL', { 
        insuredId,
        count: results.length 
      });

      return results;
    } catch (error) {
      this.logger.error('Error finding appointments by insured ID in MySQL', error as Error, {
        insuredId
      });
      throw new Error(`Failed to find appointments for insured ID in MySQL: ${(error as Error).message}`);
    }
  }

  /**
   * Finds all appointments for a specific country
   */
  public async findByCountry(countryISO: CountryISO): Promise<MySQLAppointment[]> {
    try {
      this.logger.debug('Finding appointments by country in MySQL', { countryISO });

      const sql = `
        SELECT id, insuredId, scheduleId, countryISO, status, date, createdAt, updatedAt
        FROM appointments
        WHERE countryISO = ?
        ORDER BY createdAt DESC
      `;

      const results = await this.connection.query<MySQLAppointment>(sql, [countryISO]);

      this.logger.debug('Appointments found for country in MySQL', { 
        countryISO,
        count: results.length 
      });

      return results;
    } catch (error) {
      this.logger.error('Error finding appointments by country in MySQL', error as Error, {
        countryISO
      });
      throw new Error(`Failed to find appointments for country in MySQL: ${(error as Error).message}`);
    }
  }

  /**
   * Updates an appointment
   */
  public async update(id: number, updates: Partial<MySQLAppointment>): Promise<MySQLAppointment> {
    try {
      this.logger.info('Updating appointment in MySQL', { 
        id,
        updates: Object.keys(updates) 
      });

      // Build update query dynamically
      const updateFields: string[] = [];
      const params: unknown[] = [];

      Object.entries(updates).forEach(([key, value]) => {
        if (key !== 'id' && key !== 'createdAt') { // Don't update ID or createdAt
          updateFields.push(`${key} = ?`);
          params.push(value);
        }
      });

      // Always update the updatedAt timestamp
      updateFields.push('updatedAt = NOW()');
      params.push(id); // Add ID for WHERE clause

      const sql = `
        UPDATE appointments
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `;

      const result = await this.connection.execute(sql, params);

      if (result.affectedRows === 0) {
        throw new Error('No appointment found with the specified ID');
      }

      // Fetch the updated appointment
      const updatedAppointment = await this.findById(id);
      
      if (!updatedAppointment) {
        throw new Error('Failed to retrieve updated appointment');
      }

      this.logger.info('Appointment updated successfully in MySQL', { 
        id,
        newStatus: updatedAppointment.status 
      });

      return updatedAppointment;
    } catch (error) {
      this.logger.error('Error updating appointment in MySQL', error as Error, {
        id,
        updates
      });
      throw new Error(`Failed to update appointment in MySQL: ${(error as Error).message}`);
    }
  }

  /**
   * Deletes an appointment
   */
  public async delete(id: number): Promise<boolean> {
    try {
      this.logger.info('Deleting appointment from MySQL', { id });

      const sql = 'DELETE FROM appointments WHERE id = ?';
      const result = await this.connection.execute(sql, [id]);

      const deleted = result.affectedRows > 0;

      if (deleted) {
        this.logger.info('Appointment deleted successfully from MySQL', { id });
      } else {
        this.logger.warn('No appointment found to delete in MySQL', { id });
      }

      return deleted;
    } catch (error) {
      this.logger.error('Error deleting appointment from MySQL', error as Error, { id });
      throw new Error(`Failed to delete appointment from MySQL: ${(error as Error).message}`);
    }
  }

  /**
   * Health check method to verify MySQL connectivity
   */
  public async healthCheck(): Promise<boolean> {
    try {
      await this.connection.query('SELECT 1');
      return true;
    } catch (error) {
      this.logger.error('MySQL health check failed', error as Error);
      return false;
    }
  }

  /**
   * Initialize database schema (for development/testing)
   */
  public async initializeSchema(): Promise<void> {
    try {
      this.logger.info('Initializing MySQL schema');

      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS appointments (
          id INT AUTO_INCREMENT PRIMARY KEY,
          insuredId VARCHAR(50) NOT NULL,
          scheduleId INT NOT NULL,
          countryISO ENUM('PE', 'CL') NOT NULL,
          status ENUM('pending', 'completed', 'failed') NOT NULL DEFAULT 'pending',
          date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_insuredId (insuredId),
          INDEX idx_countryISO (countryISO),
          INDEX idx_status (status),
          INDEX idx_createdAt (createdAt)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      await this.connection.execute(createTableSQL, []);

      this.logger.info('MySQL schema initialized successfully');
    } catch (error) {
      this.logger.error('Error initializing MySQL schema', error as Error);
      throw new Error(`Failed to initialize MySQL schema: ${(error as Error).message}`);
    }
  }
}
