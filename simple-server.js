const http = require('http');
const url = require('url');

const port = 3000;

// Mock data
const appointments = [
  {
    appointmentId: 'sample-1',
    insuredId: '00001',
    scheduleId: 100,
    countryISO: 'PE',
    status: 'completed'
  }
];

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');

  console.log(`${method} ${path}`);

  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // GET /
  if (path === '/' && method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({
      message: 'Rimac Medical Appointments API',
      status: 'running',
      endpoints: {
        docs: '/docs',
        appointments: '/appointments',
        health: '/health'
      }
    }));
    return;
  }

  // GET /health
  if (path === '/health' && method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({ status: 'healthy', timestamp: new Date().toISOString() }));
    return;
  }

  // GET /docs
  if (path === '/docs' && method === 'GET') {
    res.setHeader('Content-Type', 'text/html');
    res.writeHead(200);
    res.end(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Rimac Medical Appointments API</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          .endpoint { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px; }
          .method { color: white; padding: 5px 10px; border-radius: 3px; font-weight: bold; }
          .post { background: #49cc90; }
          .get { background: #61affe; }
          pre { background: #f8f8f8; padding: 10px; border-radius: 3px; overflow-x: auto; }
        </style>
      </head>
      <body>
        <h1>🏥 Rimac Medical Appointments API</h1>
        <p>Sistema de agendamiento de citas médicas para Rimac Seguros</p>
        
        <div class="endpoint">
          <h3><span class="method post">POST</span> /appointments</h3>
          <p>Crear una nueva cita médica</p>
          <h4>Request Body:</h4>
          <pre>{
  "insuredId": "00012",
  "scheduleId": 100,
  "countryISO": "PE"
}</pre>
          <h4>Ejemplo con curl:</h4>
          <pre>curl -X POST http://localhost:3000/appointments \\
  -H "Content-Type: application/json" \\
  -d '{"insuredId":"00012","scheduleId":100,"countryISO":"PE"}'</pre>
        </div>

        <div class="endpoint">
          <h3><span class="method get">GET</span> /appointments/{insuredId}</h3>
          <p>Obtener todas las citas de un asegurado</p>
          <h4>Ejemplo:</h4>
          <pre>curl http://localhost:3000/appointments/00001</pre>
        </div>

        <div class="endpoint">
          <h3><span class="method get">GET</span> /health</h3>
          <p>Verificar el estado de la API</p>
          <h4>Ejemplo:</h4>
          <pre>curl http://localhost:3000/health</pre>
        </div>

        <h2>🧪 Probar la API</h2>
        <p>Puedes probar la API usando:</p>
        <ul>
          <li><strong>Postman</strong> - Importa las URLs de arriba</li>
          <li><strong>curl</strong> - Usa los comandos de ejemplo</li>
          <li><strong>PowerShell</strong> - Usa Invoke-WebRequest</li>
        </ul>

        <script>
          console.log('🚀 Rimac Medical Appointments API Documentation Loaded');
        </script>
      </body>
      </html>
    `);
    return;
  }

  // POST /appointments
  if (path === '/appointments' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        const appointmentId = 'appointment-' + Date.now();
        
        const newAppointment = {
          appointmentId,
          insuredId: data.insuredId,
          scheduleId: data.scheduleId,
          countryISO: data.countryISO,
          status: 'pending',
          createdAt: new Date().toISOString()
        };

        appointments.push(newAppointment);

        res.writeHead(201);
        res.end(JSON.stringify({
          success: true,
          data: {
            message: 'Appointment request received and is pending',
            appointmentId,
            status: 'pending',
            timestamp: new Date().toISOString()
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({
          success: false,
          error: { message: 'Invalid JSON' }
        }));
      }
    });
    return;
  }

  // GET /appointments/:insuredId
  if (path.startsWith('/appointments/') && method === 'GET') {
    const insuredId = path.split('/')[2];
    const userAppointments = appointments.filter(apt => apt.insuredId === insuredId);
    
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: {
        appointments: userAppointments,
        count: userAppointments.length,
        insuredId
      }
    }));
    return;
  }

  // 404 Not Found
  res.writeHead(404);
  res.end(JSON.stringify({
    success: false,
    error: { message: 'Endpoint not found' }
  }));
});

server.listen(port, () => {
  console.log('🚀 Rimac Medical Appointments API running on http://localhost:' + port);
  console.log('📖 Documentation available at http://localhost:' + port + '/docs');
  console.log('❤️  Health check at http://localhost:' + port + '/health');
  console.log('');
  console.log('Ready for demo! 🎯');
});

server.on('error', (err) => {
  console.error('❌ Server error:', err.message);
  if (err.code === 'EADDRINUSE') {
    console.log('💡 Port 3000 is already in use. Try:');
    console.log('   netstat -ano | findstr :3000');
    console.log('   taskkill /PID <PID> /F');
  }
});
