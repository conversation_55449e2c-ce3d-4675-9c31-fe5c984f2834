/**
 * Peru Appointment Processing Lambda Handler
 * Processes appointments for Peru (PE) from SQS queue
 */

import { SQSEvent, Context } from 'aws-lambda';
import { MySQLRepository } from '@/repositories/MySQLRepository';
import { EventBridgeService } from '@/services/EventBridgeService';
import { createMySQLConnection } from '@/config/database';
import { getAWSClients } from '@/config/aws';
import { createRequestLogger } from '@/utils/logger';
import { Appointment } from '@/entities/Appointment';

// Global instances for Lambda container reuse
let mysqlRepository: MySQLRepository | null = null;
let eventBridgeService: EventBridgeService | null = null;

/**
 * Initialize dependencies (singleton pattern for Lambda container reuse)
 */
async function initializeDependencies(requestId: string, functionName: string): Promise<{
  mysqlRepository: MySQLRepository;
  eventBridgeService: EventBridgeService;
}> {
  const logger = createRequestLogger(requestId, functionName);

  if (!mysqlRepository || !eventBridgeService) {
    const awsClients = getAWSClients();
    const config = awsClients.getConfig();

    // Initialize MySQL connection and repository
    const mysqlConnection = createMySQLConnection(logger);
    await mysqlConnection.connect();
    
    // Initialize schema if needed (for development)
    mysqlRepository = new MySQLRepository(mysqlConnection, logger);
    await mysqlRepository.initializeSchema();

    // Initialize EventBridge service
    eventBridgeService = new EventBridgeService(
      awsClients.getEventBridgeClient(),
      config.eventBridgeBusName,
      logger
    );
  }

  return { mysqlRepository, eventBridgeService };
}

/**
 * Main Lambda handler for Peru appointments
 */
export const handler = async (event: SQSEvent, context: Context): Promise<void> => {
  const logger = createRequestLogger(context.awsRequestId, context.functionName);

  try {
    logger.info('Peru appointment processing started', {
      messageCount: event.Records.length,
      requestId: context.awsRequestId,
    });

    // Initialize dependencies
    const { mysqlRepository, eventBridgeService } = await initializeDependencies(
      context.awsRequestId,
      context.functionName
    );

    // Process each SQS message
    for (const record of event.Records) {
      try {
        logger.info('Processing Peru appointment message', {
          messageId: record.messageId,
          eventSource: record.eventSource,
        });

        // Parse message body
        let messageBody: unknown;
        try {
          messageBody = JSON.parse(record.body);
        } catch (parseError) {
          logger.error('Failed to parse SQS message body', parseError as Error, {
            messageId: record.messageId,
            body: record.body,
          });
          continue; // Skip this message
        }

        // Extract appointment data from SNS message
        const appointmentData = extractAppointmentData(messageBody);
        if (!appointmentData) {
          logger.warn('Invalid appointment message format', {
            messageId: record.messageId,
            messageBody,
          });
          continue; // Skip this message
        }

        // Validate country is Peru
        if (appointmentData.countryISO !== 'PE') {
          logger.warn('Received non-Peru appointment in Peru handler', {
            messageId: record.messageId,
            countryISO: appointmentData.countryISO,
          });
          continue; // Skip this message
        }

        // Process the appointment
        await processPeruAppointment(
          appointmentData,
          mysqlRepository,
          eventBridgeService,
          logger,
          record.messageId
        );

        logger.info('Peru appointment processed successfully', {
          messageId: record.messageId,
          appointmentId: appointmentData.appointmentId,
        });

      } catch (error) {
        logger.error('Error processing Peru appointment message', error as Error, {
          messageId: record.messageId,
        });
        
        // Re-throw to trigger retry/DLQ for this specific message
        throw error;
      }
    }

    logger.info('Peru appointment processing completed', {
      messageCount: event.Records.length,
      requestId: context.awsRequestId,
    });

  } catch (error) {
    logger.error('Unhandled error in Peru appointment handler', error as Error, {
      requestId: context.awsRequestId,
    });
    throw error;
  }
};

/**
 * Processes a Peru appointment
 */
async function processPeruAppointment(
  appointmentData: AppointmentData,
  mysqlRepository: MySQLRepository,
  eventBridgeService: EventBridgeService,
  logger: ReturnType<typeof createRequestLogger>,
  messageId: string
): Promise<void> {
  try {
    logger.info('Processing Peru appointment', {
      appointmentId: appointmentData.appointmentId,
      insuredId: appointmentData.insuredId,
      scheduleId: appointmentData.scheduleId,
    });

    // Create appointment entity for MySQL
    const appointment = new Appointment({
      appointmentId: appointmentData.appointmentId,
      insuredId: appointmentData.insuredId,
      scheduleId: appointmentData.scheduleId,
      countryISO: 'PE',
      status: 'pending',
      createdAt: appointmentData.timestamp,
      updatedAt: appointmentData.timestamp,
    });

    // Convert to MySQL format and save
    const mysqlAppointment = appointment.toMySQLFormat();
    const savedAppointment = await mysqlRepository.create(mysqlAppointment);

    logger.info('Appointment saved to Peru MySQL database', {
      appointmentId: appointmentData.appointmentId,
      mysqlId: savedAppointment.id,
    });

    // Publish success event to EventBridge
    await eventBridgeService.publishSuccessEvent(
      appointmentData.appointmentId,
      appointmentData.insuredId,
      appointmentData.scheduleId,
      'PE'
    );

    logger.info('Success event published to EventBridge', {
      appointmentId: appointmentData.appointmentId,
    });

  } catch (error) {
    logger.error('Error processing Peru appointment', error as Error, {
      appointmentId: appointmentData.appointmentId,
      messageId,
    });

    // Publish failure event to EventBridge
    try {
      await eventBridgeService.publishFailureEvent(
        appointmentData.appointmentId,
        appointmentData.insuredId,
        appointmentData.scheduleId,
        'PE',
        (error as Error).message
      );

      logger.info('Failure event published to EventBridge', {
        appointmentId: appointmentData.appointmentId,
        error: (error as Error).message,
      });
    } catch (eventError) {
      logger.error('Failed to publish failure event to EventBridge', eventError as Error, {
        appointmentId: appointmentData.appointmentId,
      });
    }

    // Re-throw the original error
    throw error;
  }
}

/**
 * Interface for appointment data extracted from SQS message
 */
interface AppointmentData {
  appointmentId: string;
  insuredId: string;
  scheduleId: number;
  countryISO: 'PE' | 'CL';
  timestamp: string;
}

/**
 * Extracts appointment data from SQS message body
 */
function extractAppointmentData(messageBody: unknown): AppointmentData | null {
  if (!messageBody || typeof messageBody !== 'object') {
    return null;
  }

  const body = messageBody as Record<string, unknown>;

  // Handle SNS message format
  let appointmentMessage: Record<string, unknown>;
  
  if (body.Message && typeof body.Message === 'string') {
    // SNS message wrapped in SQS
    try {
      appointmentMessage = JSON.parse(body.Message) as Record<string, unknown>;
    } catch {
      return null;
    }
  } else {
    // Direct message
    appointmentMessage = body;
  }

  // Validate required fields
  if (!appointmentMessage.appointmentId || typeof appointmentMessage.appointmentId !== 'string' ||
      !appointmentMessage.insuredId || typeof appointmentMessage.insuredId !== 'string' ||
      !appointmentMessage.scheduleId || typeof appointmentMessage.scheduleId !== 'number' ||
      !appointmentMessage.countryISO || typeof appointmentMessage.countryISO !== 'string' ||
      !appointmentMessage.timestamp || typeof appointmentMessage.timestamp !== 'string') {
    return null;
  }

  // Validate country ISO
  if (!['PE', 'CL'].includes(appointmentMessage.countryISO)) {
    return null;
  }

  return {
    appointmentId: appointmentMessage.appointmentId,
    insuredId: appointmentMessage.insuredId,
    scheduleId: appointmentMessage.scheduleId,
    countryISO: appointmentMessage.countryISO as 'PE' | 'CL',
    timestamp: appointmentMessage.timestamp,
  };
}
