/**
 * Data Transfer Objects (DTOs) for API requests and responses
 */

import { CountryISO, AppointmentStatus } from './Appointment';

/**
 * Request DTO for creating a new appointment
 */
export interface CreateAppointmentRequestDTO {
  insuredId: string;
  scheduleId: number;
  countryISO: CountryISO;
}

/**
 * Response DTO for appointment creation
 */
export interface CreateAppointmentResponseDTO {
  message: string;
  appointmentId: string;
  status: AppointmentStatus;
  timestamp: string;
}

/**
 * Response DTO for appointment retrieval
 */
export interface AppointmentResponseDTO {
  appointmentId: string;
  scheduleId: number;
  countryISO: CountryISO;
  status: AppointmentStatus;
  createdAt: string;
  updatedAt: string;
}

/**
 * Response DTO for listing appointments by insured ID
 */
export interface ListAppointmentsResponseDTO {
  appointments: AppointmentResponseDTO[];
  count: number;
  insuredId: string;
}

/**
 * SNS Message DTO for publishing appointment events
 */
export interface AppointmentSNSMessageDTO {
  appointmentId: string;
  insuredId: string;
  scheduleId: number;
  countryISO: CountryISO;
  timestamp: string;
}

/**
 * SQS Message DTO for processing appointments
 */
export interface AppointmentSQSMessageDTO {
  appointmentId: string;
  insuredId: string;
  scheduleId: number;
  countryISO: CountryISO;
  timestamp: string;
  messageId: string;
  receiptHandle: string;
}

/**
 * EventBridge Event DTO for appointment processing results
 */
export interface AppointmentEventBridgeEventDTO {
  source: string;
  'detail-type': string;
  detail: {
    appointmentId: string;
    insuredId: string;
    scheduleId: number;
    countryISO: CountryISO;
    status: 'success' | 'failure';
    processedAt: string;
    errorMessage?: string;
  };
}

/**
 * Error Response DTO
 */
export interface ErrorResponseDTO {
  error: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
  timestamp: string;
  path: string;
  method: string;
}

/**
 * Health Check Response DTO
 */
export interface HealthCheckResponseDTO {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version: string;
  services: {
    dynamodb: 'connected' | 'disconnected';
    mysql: 'connected' | 'disconnected';
    sns: 'available' | 'unavailable';
    sqs: 'available' | 'unavailable';
  };
}

/**
 * Validation Error DTO
 */
export interface ValidationErrorDTO {
  field: string;
  message: string;
  value?: unknown;
}

/**
 * API Response wrapper
 */
export interface APIResponseDTO<T = unknown> {
  success: boolean;
  data?: T;
  error?: ErrorResponseDTO['error'];
  timestamp: string;
  requestId?: string;
}
