/**
 * Unit Tests for DynamoDBRepository
 * Tests the DynamoDB data access layer with mocked AWS SDK
 */

import { DynamoDBRepository } from '@/repositories/DynamoDBRepository';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBAppointment } from '@/entities/Appointment';
import { ILogger } from '@/entities/interfaces';

// Mock AWS SDK
jest.mock('@aws-sdk/client-dynamodb');
jest.mock('@aws-sdk/util-dynamodb');

const mockDynamoDBClient = {
  send: jest.fn(),
} as unknown as jest.Mocked<DynamoDBClient>;

const mockLogger: jest.Mocked<ILogger> = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};

// Mock marshall/unmarshall functions
const mockMarshall = jest.fn();
const mockUnmarshall = jest.fn();

// Import mocked functions
import { marshall, unmarshall } from '@aws-sdk/util-dynamodb';
(marshall as jest.Mock) = mockMarshall;
(unmarshall as jest.Mock) = mockUnmarshall;

describe('DynamoDBRepository', () => {
  let repository: DynamoDBRepository;
  const tableName = 'test-appointments-table';

  beforeEach(() => {
    jest.clearAllMocks();
    repository = new DynamoDBRepository(mockDynamoDBClient, tableName, mockLogger);
  });

  describe('create', () => {
    const mockAppointment: DynamoDBAppointment = {
      appointmentId: 'test-uuid',
      insuredId: '00012',
      scheduleId: 100,
      countryISO: 'PE',
      status: 'pending',
      createdAt: '2024-01-15T10:30:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z',
    };

    it('should create appointment successfully', async () => {
      // Arrange
      mockMarshall.mockReturnValue({ appointmentId: { S: 'test-uuid' } });
      mockDynamoDBClient.send.mockResolvedValue({});

      // Act
      const result = await repository.create(mockAppointment);

      // Assert
      expect(result).toEqual(mockAppointment);
      expect(mockDynamoDBClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: tableName,
            ConditionExpression: 'attribute_not_exists(appointmentId)',
          }),
        })
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Appointment created successfully in DynamoDB',
        { appointmentId: 'test-uuid' }
      );
    });

    it('should handle DynamoDB error', async () => {
      // Arrange
      const dbError = new Error('ConditionalCheckFailedException');
      mockMarshall.mockReturnValue({ appointmentId: { S: 'test-uuid' } });
      mockDynamoDBClient.send.mockRejectedValue(dbError);

      // Act & Assert
      await expect(repository.create(mockAppointment)).rejects.toThrow(
        'Failed to create appointment: ConditionalCheckFailedException'
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error creating appointment in DynamoDB',
        dbError,
        expect.objectContaining({
          appointmentId: 'test-uuid',
          insuredId: '00012',
        })
      );
    });
  });

  describe('findById', () => {
    const appointmentId = 'test-uuid';
    const mockDynamoDBItem = {
      appointmentId: { S: 'test-uuid' },
      insuredId: { S: '00012' },
      scheduleId: { N: '100' },
      countryISO: { S: 'PE' },
      status: { S: 'pending' },
      createdAt: { S: '2024-01-15T10:30:00.000Z' },
      updatedAt: { S: '2024-01-15T10:30:00.000Z' },
    };

    const mockAppointment: DynamoDBAppointment = {
      appointmentId: 'test-uuid',
      insuredId: '00012',
      scheduleId: 100,
      countryISO: 'PE',
      status: 'pending',
      createdAt: '2024-01-15T10:30:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z',
    };

    it('should find appointment by ID successfully', async () => {
      // Arrange
      mockMarshall.mockReturnValue({ appointmentId: { S: appointmentId } });
      mockUnmarshall.mockReturnValue(mockAppointment);
      mockDynamoDBClient.send.mockResolvedValue({ Item: mockDynamoDBItem });

      // Act
      const result = await repository.findById(appointmentId);

      // Assert
      expect(result).toEqual(mockAppointment);
      expect(mockDynamoDBClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: tableName,
            Key: { appointmentId: { S: appointmentId } },
          }),
        })
      );
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Appointment found in DynamoDB',
        expect.objectContaining({
          appointmentId,
          status: 'pending',
        })
      );
    });

    it('should return null when appointment not found', async () => {
      // Arrange
      mockMarshall.mockReturnValue({ appointmentId: { S: appointmentId } });
      mockDynamoDBClient.send.mockResolvedValue({ Item: undefined });

      // Act
      const result = await repository.findById(appointmentId);

      // Assert
      expect(result).toBeNull();
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Appointment not found in DynamoDB',
        { appointmentId }
      );
    });

    it('should handle DynamoDB error', async () => {
      // Arrange
      const dbError = new Error('ResourceNotFoundException');
      mockMarshall.mockReturnValue({ appointmentId: { S: appointmentId } });
      mockDynamoDBClient.send.mockRejectedValue(dbError);

      // Act & Assert
      await expect(repository.findById(appointmentId)).rejects.toThrow(
        'Failed to find appointment: ResourceNotFoundException'
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error finding appointment by ID in DynamoDB',
        dbError,
        { appointmentId }
      );
    });
  });

  describe('findByInsuredId', () => {
    const insuredId = '00012';
    const mockDynamoDBItems = [
      {
        appointmentId: { S: 'appointment-1' },
        insuredId: { S: '00012' },
        scheduleId: { N: '100' },
        countryISO: { S: 'PE' },
        status: { S: 'completed' },
        createdAt: { S: '2024-01-15T10:30:00.000Z' },
        updatedAt: { S: '2024-01-15T10:35:00.000Z' },
      },
      {
        appointmentId: { S: 'appointment-2' },
        insuredId: { S: '00012' },
        scheduleId: { N: '200' },
        countryISO: { S: 'CL' },
        status: { S: 'pending' },
        createdAt: { S: '2024-01-15T11:00:00.000Z' },
        updatedAt: { S: '2024-01-15T11:00:00.000Z' },
      },
    ];

    const mockAppointments: DynamoDBAppointment[] = [
      {
        appointmentId: 'appointment-1',
        insuredId: '00012',
        scheduleId: 100,
        countryISO: 'PE',
        status: 'completed',
        createdAt: '2024-01-15T10:30:00.000Z',
        updatedAt: '2024-01-15T10:35:00.000Z',
      },
      {
        appointmentId: 'appointment-2',
        insuredId: '00012',
        scheduleId: 200,
        countryISO: 'CL',
        status: 'pending',
        createdAt: '2024-01-15T11:00:00.000Z',
        updatedAt: '2024-01-15T11:00:00.000Z',
      },
    ];

    it('should find appointments by insured ID successfully', async () => {
      // Arrange
      mockMarshall.mockReturnValue({ ':insuredId': { S: insuredId } });
      mockUnmarshall
        .mockReturnValueOnce(mockAppointments[0])
        .mockReturnValueOnce(mockAppointments[1]);
      mockDynamoDBClient.send.mockResolvedValue({ Items: mockDynamoDBItems });

      // Act
      const result = await repository.findByInsuredId(insuredId);

      // Assert
      expect(result).toEqual(mockAppointments);
      expect(mockDynamoDBClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: tableName,
            IndexName: 'InsuredIdIndex',
            KeyConditionExpression: 'insuredId = :insuredId',
          }),
        })
      );
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Appointments found for insured ID in DynamoDB',
        expect.objectContaining({
          insuredId,
          count: 2,
        })
      );
    });

    it('should return empty array when no appointments found', async () => {
      // Arrange
      mockMarshall.mockReturnValue({ ':insuredId': { S: insuredId } });
      mockDynamoDBClient.send.mockResolvedValue({ Items: [] });

      // Act
      const result = await repository.findByInsuredId(insuredId);

      // Assert
      expect(result).toEqual([]);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'No appointments found for insured ID in DynamoDB',
        { insuredId }
      );
    });

    it('should handle DynamoDB error', async () => {
      // Arrange
      const dbError = new Error('ValidationException');
      mockMarshall.mockReturnValue({ ':insuredId': { S: insuredId } });
      mockDynamoDBClient.send.mockRejectedValue(dbError);

      // Act & Assert
      await expect(repository.findByInsuredId(insuredId)).rejects.toThrow(
        'Failed to find appointments for insured ID: ValidationException'
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error finding appointments by insured ID in DynamoDB',
        dbError,
        { insuredId }
      );
    });
  });

  describe('update', () => {
    const appointmentId = 'test-uuid';
    const updates = { status: 'completed' as const };
    const mockUpdatedItem = {
      appointmentId: { S: 'test-uuid' },
      insuredId: { S: '00012' },
      scheduleId: { N: '100' },
      countryISO: { S: 'PE' },
      status: { S: 'completed' },
      createdAt: { S: '2024-01-15T10:30:00.000Z' },
      updatedAt: { S: '2024-01-15T10:35:00.000Z' },
    };

    const mockUpdatedAppointment: DynamoDBAppointment = {
      appointmentId: 'test-uuid',
      insuredId: '00012',
      scheduleId: 100,
      countryISO: 'PE',
      status: 'completed',
      createdAt: '2024-01-15T10:30:00.000Z',
      updatedAt: '2024-01-15T10:35:00.000Z',
    };

    it('should update appointment successfully', async () => {
      // Arrange
      mockMarshall
        .mockReturnValueOnce({ appointmentId: { S: appointmentId } })
        .mockReturnValueOnce({ ':val0': { S: 'completed' }, ':updatedAt': { S: expect.any(String) } });
      mockUnmarshall.mockReturnValue(mockUpdatedAppointment);
      mockDynamoDBClient.send.mockResolvedValue({ Attributes: mockUpdatedItem });

      // Act
      const result = await repository.update(appointmentId, updates);

      // Assert
      expect(result).toEqual(mockUpdatedAppointment);
      expect(mockDynamoDBClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: tableName,
            Key: { appointmentId: { S: appointmentId } },
            ReturnValues: 'ALL_NEW',
          }),
        })
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Appointment updated successfully in DynamoDB',
        expect.objectContaining({
          appointmentId,
          newStatus: 'completed',
        })
      );
    });

    it('should handle update error', async () => {
      // Arrange
      const dbError = new Error('ConditionalCheckFailedException');
      mockMarshall
        .mockReturnValueOnce({ appointmentId: { S: appointmentId } })
        .mockReturnValueOnce({ ':val0': { S: 'completed' } });
      mockDynamoDBClient.send.mockRejectedValue(dbError);

      // Act & Assert
      await expect(repository.update(appointmentId, updates)).rejects.toThrow(
        'Failed to update appointment: ConditionalCheckFailedException'
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error updating appointment in DynamoDB',
        dbError,
        expect.objectContaining({
          appointmentId,
          updates,
        })
      );
    });
  });

  describe('delete', () => {
    const appointmentId = 'test-uuid';

    it('should delete appointment successfully', async () => {
      // Arrange
      mockMarshall.mockReturnValue({ appointmentId: { S: appointmentId } });
      mockDynamoDBClient.send.mockResolvedValue({});

      // Act
      const result = await repository.delete(appointmentId);

      // Assert
      expect(result).toBe(true);
      expect(mockDynamoDBClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: tableName,
            Key: { appointmentId: { S: appointmentId } },
            ConditionExpression: 'attribute_exists(appointmentId)',
          }),
        })
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Appointment deleted successfully from DynamoDB',
        { appointmentId }
      );
    });

    it('should return false when appointment does not exist', async () => {
      // Arrange
      const conditionalError = new Error('ConditionalCheckFailedException');
      conditionalError.name = 'ConditionalCheckFailedException';
      mockMarshall.mockReturnValue({ appointmentId: { S: appointmentId } });
      mockDynamoDBClient.send.mockRejectedValue(conditionalError);

      // Act
      const result = await repository.delete(appointmentId);

      // Assert
      expect(result).toBe(false);
    });

    it('should handle other DynamoDB errors', async () => {
      // Arrange
      const dbError = new Error('InternalServerError');
      mockMarshall.mockReturnValue({ appointmentId: { S: appointmentId } });
      mockDynamoDBClient.send.mockRejectedValue(dbError);

      // Act & Assert
      await expect(repository.delete(appointmentId)).rejects.toThrow(
        'Failed to delete appointment: InternalServerError'
      );
    });
  });

  describe('healthCheck', () => {
    it('should return true when DynamoDB is healthy', async () => {
      // Arrange
      mockDynamoDBClient.send.mockResolvedValue({ Items: [] });

      // Act
      const result = await repository.healthCheck();

      // Assert
      expect(result).toBe(true);
    });

    it('should return false when DynamoDB is unhealthy', async () => {
      // Arrange
      const dbError = new Error('ServiceUnavailable');
      mockDynamoDBClient.send.mockRejectedValue(dbError);

      // Act
      const result = await repository.healthCheck();

      // Assert
      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'DynamoDB health check failed',
        dbError
      );
    });
  });
});
