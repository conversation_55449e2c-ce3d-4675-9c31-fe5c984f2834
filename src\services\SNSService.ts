/**
 * SNS Service - Handles Amazon SNS operations
 * Publishes appointment events to SNS topics with country filtering
 */

import { SNSClient, PublishCommand } from '@aws-sdk/client-sns';
import { AppointmentSNSMessageDTO } from '@/entities/DTOs';
import { ISNSService, ILogger } from '@/entities/interfaces';

export class SNSService implements ISNSService {
  private snsClient: SNSClient;
  private topicArn: string;
  private logger: ILogger;

  constructor(snsClient: SNSClient, topicArn: string, logger: ILogger) {
    this.snsClient = snsClient;
    this.topicArn = topicArn;
    this.logger = logger;
  }

  /**
   * Publishes an appointment event to SNS topic
   * Uses message attributes for country-based filtering
   */
  public async publishAppointmentEvent(message: AppointmentSNSMessageDTO): Promise<string> {
    try {
      this.logger.info('Publishing appointment event to SNS', {
        appointmentId: message.appointmentId,
        countryISO: message.countryISO,
        topicArn: this.topicArn,
      });

      // Validate message
      this.validateSNSMessage(message);

      // Prepare SNS message
      const snsMessage = {
        appointmentId: message.appointmentId,
        insuredId: message.insuredId,
        scheduleId: message.scheduleId,
        countryISO: message.countryISO,
        timestamp: message.timestamp,
        eventType: 'AppointmentCreated',
        source: 'rimac.appointments',
      };

      // Create publish command with message attributes for filtering
      const command = new PublishCommand({
        TopicArn: this.topicArn,
        Message: JSON.stringify(snsMessage),
        MessageAttributes: {
          countryISO: {
            DataType: 'String',
            StringValue: message.countryISO,
          },
          eventType: {
            DataType: 'String',
            StringValue: 'AppointmentCreated',
          },
          source: {
            DataType: 'String',
            StringValue: 'rimac.appointments',
          },
        },
        Subject: `Medical Appointment Created - ${message.countryISO}`,
      });

      // Publish message
      const result = await this.snsClient.send(command);

      if (!result.MessageId) {
        throw new Error('Failed to get MessageId from SNS publish result');
      }

      this.logger.info('Appointment event published successfully to SNS', {
        appointmentId: message.appointmentId,
        countryISO: message.countryISO,
        messageId: result.MessageId,
      });

      return result.MessageId;
    } catch (error) {
      this.logger.error('Error publishing appointment event to SNS', error as Error, {
        appointmentId: message.appointmentId,
        countryISO: message.countryISO,
        topicArn: this.topicArn,
      });
      throw new Error(`Failed to publish SNS message: ${(error as Error).message}`);
    }
  }

  /**
   * Publishes a test message to verify SNS connectivity
   */
  public async publishTestMessage(): Promise<string> {
    try {
      this.logger.info('Publishing test message to SNS', { topicArn: this.topicArn });

      const testMessage = {
        eventType: 'HealthCheck',
        timestamp: new Date().toISOString(),
        source: 'rimac.appointments',
        message: 'SNS connectivity test',
      };

      const command = new PublishCommand({
        TopicArn: this.topicArn,
        Message: JSON.stringify(testMessage),
        MessageAttributes: {
          eventType: {
            DataType: 'String',
            StringValue: 'HealthCheck',
          },
          source: {
            DataType: 'String',
            StringValue: 'rimac.appointments',
          },
        },
        Subject: 'SNS Health Check',
      });

      const result = await this.snsClient.send(command);

      if (!result.MessageId) {
        throw new Error('Failed to get MessageId from SNS test publish');
      }

      this.logger.info('Test message published successfully to SNS', {
        messageId: result.MessageId,
      });

      return result.MessageId;
    } catch (error) {
      this.logger.error('Error publishing test message to SNS', error as Error, {
        topicArn: this.topicArn,
      });
      throw new Error(`Failed to publish SNS test message: ${(error as Error).message}`);
    }
  }

  /**
   * Validates SNS message before publishing
   */
  private validateSNSMessage(message: AppointmentSNSMessageDTO): void {
    if (!message) {
      throw new Error('SNS message is required');
    }

    if (!message.appointmentId || typeof message.appointmentId !== 'string') {
      throw new Error('Valid appointmentId is required in SNS message');
    }

    if (!message.insuredId || typeof message.insuredId !== 'string') {
      throw new Error('Valid insuredId is required in SNS message');
    }

    if (!message.scheduleId || typeof message.scheduleId !== 'number' || message.scheduleId <= 0) {
      throw new Error('Valid scheduleId is required in SNS message');
    }

    if (!message.countryISO || !['PE', 'CL'].includes(message.countryISO)) {
      throw new Error('Valid countryISO is required in SNS message (must be PE or CL)');
    }

    if (!message.timestamp || typeof message.timestamp !== 'string') {
      throw new Error('Valid timestamp is required in SNS message');
    }

    // Validate timestamp format (ISO 8601)
    try {
      const date = new Date(message.timestamp);
      if (isNaN(date.getTime())) {
        throw new Error('Invalid timestamp format');
      }
    } catch {
      throw new Error('Invalid timestamp format in SNS message');
    }
  }

  /**
   * Health check method to verify SNS connectivity
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Try to publish a test message
      await this.publishTestMessage();
      return true;
    } catch (error) {
      this.logger.error('SNS health check failed', error as Error);
      return false;
    }
  }
}
