/**
 * Appointment Service - Business Logic Layer
 * Handles the core business logic for medical appointments
 */

import { v4 as uuidv4 } from 'uuid';
import { Appointment, DynamoDBAppointment, AppointmentStatus } from '@/entities/Appointment';
import {
  CreateAppointmentRequestDTO,
  CreateAppointmentResponseDTO,
  ListAppointmentsResponseDTO,
  AppointmentResponseDTO,
  AppointmentSNSMessageDTO,
} from '@/entities/DTOs';
import {
  IAppointmentService,
  IDynamoDBRepository,
  ISNSService,
  ILogger,
} from '@/entities/interfaces';

export class AppointmentService implements IAppointmentService {
  private dynamodbRepository: IDynamoDBRepository;
  private snsService: ISNSService;
  private logger: ILogger;

  constructor(
    dynamodbRepository: IDynamoDBRepository,
    snsService: ISNSService,
    logger: ILogger
  ) {
    this.dynamodbRepository = dynamodbRepository;
    this.snsService = snsService;
    this.logger = logger;
  }

  /**
   * Creates a new medical appointment
   * 1. Validates the request
   * 2. Creates appointment in DynamoDB with 'pending' status
   * 3. Publishes event to SNS for country-specific processing
   */
  public async createAppointment(
    request: CreateAppointmentRequestDTO
  ): Promise<CreateAppointmentResponseDTO> {
    try {
      this.logger.info('Creating new appointment', {
        insuredId: request.insuredId,
        scheduleId: request.scheduleId,
        countryISO: request.countryISO,
      });

      // Validate request
      this.validateCreateAppointmentRequest(request);

      // Generate unique appointment ID
      const appointmentId = uuidv4();
      const timestamp = new Date().toISOString();

      // Create appointment entity
      const appointment = new Appointment({
        appointmentId,
        insuredId: request.insuredId,
        scheduleId: request.scheduleId,
        countryISO: request.countryISO,
        status: 'pending',
        createdAt: timestamp,
        updatedAt: timestamp,
      });

      // Validate appointment
      if (!appointment.isValid()) {
        throw new Error('Invalid appointment data');
      }

      // Save to DynamoDB
      await this.dynamodbRepository.create(appointment);

      // Publish to SNS for country-specific processing
      const snsMessage: AppointmentSNSMessageDTO = {
        appointmentId,
        insuredId: request.insuredId,
        scheduleId: request.scheduleId,
        countryISO: request.countryISO,
        timestamp,
      };

      const messageId = await this.snsService.publishAppointmentEvent(snsMessage);

      this.logger.info('Appointment created successfully', {
        appointmentId,
        insuredId: request.insuredId,
        snsMessageId: messageId,
      });

      return {
        message: 'Appointment request received and is pending',
        appointmentId,
        status: 'pending',
        timestamp,
      };
    } catch (error) {
      this.logger.error('Error creating appointment', error as Error, {
        insuredId: request.insuredId,
        scheduleId: request.scheduleId,
        countryISO: request.countryISO,
      });
      throw error;
    }
  }

  /**
   * Retrieves all appointments for a specific insured ID
   */
  public async getAppointmentsByInsuredId(insuredId: string): Promise<ListAppointmentsResponseDTO> {
    try {
      this.logger.info('Retrieving appointments for insured ID', { insuredId });

      // Validate insured ID
      if (!insuredId || typeof insuredId !== 'string' || insuredId.trim().length === 0) {
        throw new Error('Invalid insured ID provided');
      }

      // Fetch appointments from DynamoDB
      const appointments = await this.dynamodbRepository.findByInsuredId(insuredId.trim());

      // Transform to response DTOs
      const appointmentDTOs: AppointmentResponseDTO[] = appointments.map(appointment => ({
        appointmentId: appointment.appointmentId,
        scheduleId: appointment.scheduleId,
        countryISO: appointment.countryISO,
        status: appointment.status,
        createdAt: appointment.createdAt,
        updatedAt: appointment.updatedAt,
      }));

      // Sort by creation date (newest first)
      appointmentDTOs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      this.logger.info('Appointments retrieved successfully', {
        insuredId,
        count: appointmentDTOs.length,
      });

      return {
        appointments: appointmentDTOs,
        count: appointmentDTOs.length,
        insuredId,
      };
    } catch (error) {
      this.logger.error('Error retrieving appointments for insured ID', error as Error, {
        insuredId,
      });
      throw error;
    }
  }

  /**
   * Updates the status of an appointment
   */
  public async updateAppointmentStatus(
    appointmentId: string,
    status: AppointmentStatus
  ): Promise<void> {
    try {
      this.logger.info('Updating appointment status', { appointmentId, status });

      // Validate inputs
      if (!appointmentId || typeof appointmentId !== 'string') {
        throw new Error('Invalid appointment ID provided');
      }

      if (!['pending', 'completed', 'failed'].includes(status)) {
        throw new Error('Invalid status provided');
      }

      // Check if appointment exists
      const existingAppointment = await this.dynamodbRepository.findById(appointmentId);
      if (!existingAppointment) {
        throw new Error('Appointment not found');
      }

      // Update appointment status
      await this.dynamodbRepository.update(appointmentId, {
        status,
        updatedAt: new Date().toISOString(),
      });

      this.logger.info('Appointment status updated successfully', {
        appointmentId,
        oldStatus: existingAppointment.status,
        newStatus: status,
      });
    } catch (error) {
      this.logger.error('Error updating appointment status', error as Error, {
        appointmentId,
        status,
      });
      throw error;
    }
  }

  /**
   * Processes appointment confirmation from EventBridge
   * Updates appointment status based on processing result
   */
  public async processAppointmentConfirmation(
    appointmentId: string,
    success: boolean
  ): Promise<void> {
    try {
      this.logger.info('Processing appointment confirmation', { appointmentId, success });

      // Validate appointment ID
      if (!appointmentId || typeof appointmentId !== 'string') {
        throw new Error('Invalid appointment ID provided');
      }

      // Check if appointment exists
      const existingAppointment = await this.dynamodbRepository.findById(appointmentId);
      if (!existingAppointment) {
        this.logger.warn('Appointment not found for confirmation', { appointmentId });
        return; // Don't throw error for missing appointments in confirmation flow
      }

      // Determine new status based on success
      const newStatus: AppointmentStatus = success ? 'completed' : 'failed';

      // Update appointment status
      await this.updateAppointmentStatus(appointmentId, newStatus);

      this.logger.info('Appointment confirmation processed successfully', {
        appointmentId,
        success,
        newStatus,
      });
    } catch (error) {
      this.logger.error('Error processing appointment confirmation', error as Error, {
        appointmentId,
        success,
      });
      throw error;
    }
  }

  /**
   * Validates create appointment request
   */
  private validateCreateAppointmentRequest(request: CreateAppointmentRequestDTO): void {
    if (!request) {
      throw new Error('Request body is required');
    }

    if (!request.insuredId || typeof request.insuredId !== 'string' || request.insuredId.trim().length === 0) {
      throw new Error('Valid insuredId is required');
    }

    if (!request.scheduleId || typeof request.scheduleId !== 'number' || request.scheduleId <= 0) {
      throw new Error('Valid scheduleId is required (must be a positive number)');
    }

    if (!request.countryISO || !['PE', 'CL'].includes(request.countryISO)) {
      throw new Error('Valid countryISO is required (must be PE or CL)');
    }

    // Additional business validations can be added here
    if (request.insuredId.length > 50) {
      throw new Error('InsuredId must not exceed 50 characters');
    }

    if (request.scheduleId > 999999) {
      throw new Error('ScheduleId must not exceed 999999');
    }
  }
}
