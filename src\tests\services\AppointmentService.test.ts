/**
 * Unit Tests for AppointmentService
 * Tests the core business logic for appointment management
 */

import { AppointmentService } from '@/services/AppointmentService';
import { DynamoDBAppointment } from '@/entities/Appointment';
import { CreateAppointmentRequestDTO } from '@/entities/DTOs';
import { IDynamoDBRepository, ISNSService, ILogger } from '@/entities/interfaces';

// Mock dependencies
const mockDynamoDBRepository: jest.Mocked<IDynamoDBRepository> = {
  create: jest.fn(),
  findById: jest.fn(),
  findByInsuredId: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

const mockSNSService: jest.Mocked<ISNSService> = {
  publishAppointmentEvent: jest.fn(),
};

const mockLogger: jest.Mocked<ILogger> = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};

describe('AppointmentService', () => {
  let appointmentService: AppointmentService;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Create service instance
    appointmentService = new AppointmentService(
      mockDynamoDBRepository,
      mockSNSService,
      mockLogger
    );
  });

  describe('createAppointment', () => {
    const validRequest: CreateAppointmentRequestDTO = {
      insuredId: '00012',
      scheduleId: 100,
      countryISO: 'PE',
    };

    it('should create appointment successfully', async () => {
      // Arrange
      const mockAppointment: DynamoDBAppointment = {
        appointmentId: 'test-uuid',
        insuredId: '00012',
        scheduleId: 100,
        countryISO: 'PE',
        status: 'pending',
        createdAt: '2024-01-15T10:30:00.000Z',
        updatedAt: '2024-01-15T10:30:00.000Z',
      };

      mockDynamoDBRepository.create.mockResolvedValue(mockAppointment);
      mockSNSService.publishAppointmentEvent.mockResolvedValue('sns-message-id');

      // Act
      const result = await appointmentService.createAppointment(validRequest);

      // Assert
      expect(result).toEqual({
        message: 'Appointment request received and is pending',
        appointmentId: expect.any(String),
        status: 'pending',
        timestamp: expect.any(String),
      });

      expect(mockDynamoDBRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          insuredId: '00012',
          scheduleId: 100,
          countryISO: 'PE',
          status: 'pending',
        })
      );

      expect(mockSNSService.publishAppointmentEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          insuredId: '00012',
          scheduleId: 100,
          countryISO: 'PE',
        })
      );

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Creating new appointment',
        expect.any(Object)
      );
    });

    it('should throw error for invalid insuredId', async () => {
      // Arrange
      const invalidRequest = { ...validRequest, insuredId: '' };

      // Act & Assert
      await expect(appointmentService.createAppointment(invalidRequest))
        .rejects.toThrow('Valid insuredId is required');

      expect(mockDynamoDBRepository.create).not.toHaveBeenCalled();
      expect(mockSNSService.publishAppointmentEvent).not.toHaveBeenCalled();
    });

    it('should throw error for invalid scheduleId', async () => {
      // Arrange
      const invalidRequest = { ...validRequest, scheduleId: 0 };

      // Act & Assert
      await expect(appointmentService.createAppointment(invalidRequest))
        .rejects.toThrow('Valid scheduleId is required (must be a positive number)');
    });

    it('should throw error for invalid countryISO', async () => {
      // Arrange
      const invalidRequest = { ...validRequest, countryISO: 'US' as any };

      // Act & Assert
      await expect(appointmentService.createAppointment(invalidRequest))
        .rejects.toThrow('Valid countryISO is required (must be PE or CL)');
    });

    it('should handle DynamoDB error', async () => {
      // Arrange
      const dbError = new Error('DynamoDB connection failed');
      mockDynamoDBRepository.create.mockRejectedValue(dbError);

      // Act & Assert
      await expect(appointmentService.createAppointment(validRequest))
        .rejects.toThrow('DynamoDB connection failed');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error creating appointment',
        dbError,
        expect.any(Object)
      );
    });

    it('should handle SNS error', async () => {
      // Arrange
      const mockAppointment: DynamoDBAppointment = {
        appointmentId: 'test-uuid',
        insuredId: '00012',
        scheduleId: 100,
        countryISO: 'PE',
        status: 'pending',
        createdAt: '2024-01-15T10:30:00.000Z',
        updatedAt: '2024-01-15T10:30:00.000Z',
      };

      const snsError = new Error('SNS publish failed');
      mockDynamoDBRepository.create.mockResolvedValue(mockAppointment);
      mockSNSService.publishAppointmentEvent.mockRejectedValue(snsError);

      // Act & Assert
      await expect(appointmentService.createAppointment(validRequest))
        .rejects.toThrow('SNS publish failed');
    });
  });

  describe('getAppointmentsByInsuredId', () => {
    const mockAppointments: DynamoDBAppointment[] = [
      {
        appointmentId: 'appointment-1',
        insuredId: '00012',
        scheduleId: 100,
        countryISO: 'PE',
        status: 'completed',
        createdAt: '2024-01-15T10:30:00.000Z',
        updatedAt: '2024-01-15T10:35:00.000Z',
      },
      {
        appointmentId: 'appointment-2',
        insuredId: '00012',
        scheduleId: 200,
        countryISO: 'CL',
        status: 'pending',
        createdAt: '2024-01-15T11:00:00.000Z',
        updatedAt: '2024-01-15T11:00:00.000Z',
      },
    ];

    it('should retrieve appointments successfully', async () => {
      // Arrange
      mockDynamoDBRepository.findByInsuredId.mockResolvedValue(mockAppointments);

      // Act
      const result = await appointmentService.getAppointmentsByInsuredId('00012');

      // Assert
      expect(result).toEqual({
        appointments: expect.arrayContaining([
          expect.objectContaining({
            appointmentId: 'appointment-2',
            status: 'pending',
          }),
          expect.objectContaining({
            appointmentId: 'appointment-1',
            status: 'completed',
          }),
        ]),
        count: 2,
        insuredId: '00012',
      });

      expect(mockDynamoDBRepository.findByInsuredId).toHaveBeenCalledWith('00012');
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Retrieving appointments for insured ID',
        { insuredId: '00012' }
      );
    });

    it('should return empty array when no appointments found', async () => {
      // Arrange
      mockDynamoDBRepository.findByInsuredId.mockResolvedValue([]);

      // Act
      const result = await appointmentService.getAppointmentsByInsuredId('00012');

      // Assert
      expect(result).toEqual({
        appointments: [],
        count: 0,
        insuredId: '00012',
      });
    });

    it('should throw error for invalid insuredId', async () => {
      // Act & Assert
      await expect(appointmentService.getAppointmentsByInsuredId(''))
        .rejects.toThrow('Invalid insured ID provided');

      expect(mockDynamoDBRepository.findByInsuredId).not.toHaveBeenCalled();
    });

    it('should handle repository error', async () => {
      // Arrange
      const dbError = new Error('Database query failed');
      mockDynamoDBRepository.findByInsuredId.mockRejectedValue(dbError);

      // Act & Assert
      await expect(appointmentService.getAppointmentsByInsuredId('00012'))
        .rejects.toThrow('Database query failed');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error retrieving appointments for insured ID',
        dbError,
        { insuredId: '00012' }
      );
    });
  });

  describe('updateAppointmentStatus', () => {
    const mockAppointment: DynamoDBAppointment = {
      appointmentId: 'test-appointment-id',
      insuredId: '00012',
      scheduleId: 100,
      countryISO: 'PE',
      status: 'pending',
      createdAt: '2024-01-15T10:30:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z',
    };

    it('should update appointment status successfully', async () => {
      // Arrange
      mockDynamoDBRepository.findById.mockResolvedValue(mockAppointment);
      mockDynamoDBRepository.update.mockResolvedValue({
        ...mockAppointment,
        status: 'completed',
        updatedAt: '2024-01-15T10:35:00.000Z',
      });

      // Act
      await appointmentService.updateAppointmentStatus('test-appointment-id', 'completed');

      // Assert
      expect(mockDynamoDBRepository.findById).toHaveBeenCalledWith('test-appointment-id');
      expect(mockDynamoDBRepository.update).toHaveBeenCalledWith(
        'test-appointment-id',
        expect.objectContaining({
          status: 'completed',
          updatedAt: expect.any(String),
        })
      );

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Appointment status updated successfully',
        expect.objectContaining({
          appointmentId: 'test-appointment-id',
          oldStatus: 'pending',
          newStatus: 'completed',
        })
      );
    });

    it('should throw error when appointment not found', async () => {
      // Arrange
      mockDynamoDBRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(appointmentService.updateAppointmentStatus('non-existent-id', 'completed'))
        .rejects.toThrow('Appointment not found');

      expect(mockDynamoDBRepository.update).not.toHaveBeenCalled();
    });

    it('should throw error for invalid status', async () => {
      // Act & Assert
      await expect(appointmentService.updateAppointmentStatus('test-id', 'invalid' as any))
        .rejects.toThrow('Invalid status provided');

      expect(mockDynamoDBRepository.findById).not.toHaveBeenCalled();
    });
  });

  describe('processAppointmentConfirmation', () => {
    const mockAppointment: DynamoDBAppointment = {
      appointmentId: 'test-appointment-id',
      insuredId: '00012',
      scheduleId: 100,
      countryISO: 'PE',
      status: 'pending',
      createdAt: '2024-01-15T10:30:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z',
    };

    it('should process successful confirmation', async () => {
      // Arrange
      mockDynamoDBRepository.findById.mockResolvedValue(mockAppointment);
      mockDynamoDBRepository.update.mockResolvedValue({
        ...mockAppointment,
        status: 'completed',
      });

      // Act
      await appointmentService.processAppointmentConfirmation('test-appointment-id', true);

      // Assert
      expect(mockDynamoDBRepository.update).toHaveBeenCalledWith(
        'test-appointment-id',
        expect.objectContaining({ status: 'completed' })
      );
    });

    it('should process failed confirmation', async () => {
      // Arrange
      mockDynamoDBRepository.findById.mockResolvedValue(mockAppointment);
      mockDynamoDBRepository.update.mockResolvedValue({
        ...mockAppointment,
        status: 'failed',
      });

      // Act
      await appointmentService.processAppointmentConfirmation('test-appointment-id', false);

      // Assert
      expect(mockDynamoDBRepository.update).toHaveBeenCalledWith(
        'test-appointment-id',
        expect.objectContaining({ status: 'failed' })
      );
    });

    it('should handle missing appointment gracefully', async () => {
      // Arrange
      mockDynamoDBRepository.findById.mockResolvedValue(null);

      // Act
      await appointmentService.processAppointmentConfirmation('non-existent-id', true);

      // Assert
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Appointment not found for confirmation',
        { appointmentId: 'non-existent-id' }
      );
      expect(mockDynamoDBRepository.update).not.toHaveBeenCalled();
    });
  });
});
