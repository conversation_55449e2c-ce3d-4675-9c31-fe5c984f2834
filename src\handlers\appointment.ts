/**
 * Main Appointment Lambda Handler
 * Handles HTTP requests and SQS confirmation messages
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult, SQSEvent, Context } from 'aws-lambda';
import { AppointmentController } from '@/controllers/AppointmentController';
import { AppointmentService } from '@/services/AppointmentService';
import { SNSService } from '@/services/SNSService';
import { DynamoDBRepository } from '@/repositories/DynamoDBRepository';
import { getAWSClients } from '@/config/aws';
import { createRequestLogger } from '@/utils/logger';

// Global instances for Lambda container reuse
let appointmentController: AppointmentController | null = null;

/**
 * Initialize dependencies (singleton pattern for Lambda container reuse)
 */
function initializeDependencies(requestId: string, functionName: string): AppointmentController {
  if (!appointmentController) {
    const logger = createRequestLogger(requestId, functionName);
    const awsClients = getAWSClients();
    const config = awsClients.getConfig();

    // Initialize repositories
    const dynamodbRepository = new DynamoDBRepository(
      awsClients.getDynamoDBClient(),
      config.dynamodbTableName,
      logger
    );

    // Initialize services
    const snsService = new SNSService(
      awsClients.getSNSClient(),
      config.snsTopicArn,
      logger
    );

    const appointmentService = new AppointmentService(
      dynamodbRepository,
      snsService,
      logger
    );

    // Initialize controller
    appointmentController = new AppointmentController(appointmentService, logger);
  }

  return appointmentController;
}

/**
 * Main Lambda handler - Routes between HTTP and SQS events
 */
export const handler = async (
  event: APIGatewayProxyEvent | SQSEvent,
  context: Context
): Promise<APIGatewayProxyResult | void> => {
  const logger = createRequestLogger(context.awsRequestId, context.functionName);

  try {
    logger.info('Lambda invocation started', {
      eventType: 'httpMethod' in event ? 'HTTP' : 'SQS',
      requestId: context.awsRequestId,
    });

    // Initialize dependencies
    const controller = initializeDependencies(context.awsRequestId, context.functionName);

    // Route based on event type
    if ('httpMethod' in event) {
      // Handle HTTP requests (API Gateway)
      return await handleHttpRequest(event, controller, logger);
    } else {
      // Handle SQS messages (confirmation queue)
      await handleSQSMessages(event as SQSEvent, controller, logger);
      return;
    }
  } catch (error) {
    logger.error('Unhandled error in Lambda handler', error as Error, {
      requestId: context.awsRequestId,
    });

    // For HTTP requests, return error response
    if ('httpMethod' in event) {
      return {
        statusCode: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'An unexpected error occurred',
          },
          timestamp: new Date().toISOString(),
          requestId: context.awsRequestId,
        }),
      };
    }

    // For SQS messages, throw error to trigger retry/DLQ
    throw error;
  }
};

/**
 * Handles HTTP requests from API Gateway
 */
async function handleHttpRequest(
  event: APIGatewayProxyEvent,
  controller: AppointmentController,
  logger: ReturnType<typeof createRequestLogger>
): Promise<APIGatewayProxyResult> {
  const method = event.httpMethod;
  const path = event.path;

  logger.info('Processing HTTP request', { method, path });

  // Handle CORS preflight requests
  if (method === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,OPTIONS',
      },
      body: '',
    };
  }

  // Route to appropriate controller method
  if (method === 'POST' && path === '/appointments') {
    return await controller.createAppointment(event);
  } else if (method === 'GET' && path.startsWith('/appointments/')) {
    return await controller.getAppointmentsByInsuredId(event);
  } else {
    logger.warn('Unsupported route', { method, path });
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Route not found',
        },
        timestamp: new Date().toISOString(),
        requestId: event.requestContext.requestId,
      }),
    };
  }
}

/**
 * Handles SQS messages from confirmation queue
 */
async function handleSQSMessages(
  event: SQSEvent,
  controller: AppointmentController,
  logger: ReturnType<typeof createRequestLogger>
): Promise<void> {
  logger.info('Processing SQS messages', { messageCount: event.Records.length });

  // Process each message
  for (const record of event.Records) {
    try {
      logger.info('Processing SQS message', { 
        messageId: record.messageId,
        eventSource: record.eventSource,
      });

      // Parse message body
      let messageBody: unknown;
      try {
        messageBody = JSON.parse(record.body);
      } catch (parseError) {
        logger.error('Failed to parse SQS message body', parseError as Error, {
          messageId: record.messageId,
          body: record.body,
        });
        continue; // Skip this message
      }

      // Extract appointment confirmation data
      const confirmationData = extractConfirmationData(messageBody);
      if (!confirmationData) {
        logger.warn('Invalid confirmation message format', {
          messageId: record.messageId,
          messageBody,
        });
        continue; // Skip this message
      }

      // Process confirmation
      await controller.processConfirmation(
        confirmationData.appointmentId,
        confirmationData.success,
        record.messageId
      );

      logger.info('SQS message processed successfully', {
        messageId: record.messageId,
        appointmentId: confirmationData.appointmentId,
      });

    } catch (error) {
      logger.error('Error processing SQS message', error as Error, {
        messageId: record.messageId,
      });
      
      // Re-throw to trigger retry/DLQ for this specific message
      throw error;
    }
  }
}

/**
 * Extracts confirmation data from SQS message
 */
function extractConfirmationData(messageBody: unknown): { appointmentId: string; success: boolean } | null {
  if (!messageBody || typeof messageBody !== 'object') {
    return null;
  }

  const body = messageBody as Record<string, unknown>;

  // Handle EventBridge events
  if (body.detail && typeof body.detail === 'object') {
    const detail = body.detail as Record<string, unknown>;
    
    if (detail.appointmentId && typeof detail.appointmentId === 'string' &&
        detail.status && typeof detail.status === 'string') {
      return {
        appointmentId: detail.appointmentId,
        success: detail.status === 'success',
      };
    }
  }

  // Handle direct confirmation messages
  if (body.appointmentId && typeof body.appointmentId === 'string' &&
      body.success !== undefined && typeof body.success === 'boolean') {
    return {
      appointmentId: body.appointmentId,
      success: body.success,
    };
  }

  return null;
}
