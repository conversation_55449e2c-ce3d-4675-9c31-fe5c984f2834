/**
 * Appointment Controller - <PERSON><PERSON> HTTP requests for appointments
 * Implements clean architecture principles with proper separation of concerns
 */

import { 
  CreateAppointmentRequestDTO, 
  CreateAppointmentResponseDTO,
  ListAppointmentsResponseDTO,
  ErrorResponseDTO,
  APIResponseDTO,
} from '@/entities/DTOs';
import { 
  IAppointmentService, 
  IAPIGatewayEvent, 
  IHTTPResponse, 
  ILogger 
} from '@/entities/interfaces';

export class AppointmentController {
  private appointmentService: IAppointmentService;
  private logger: ILogger;

  constructor(appointmentService: IAppointmentService, logger: ILogger) {
    this.appointmentService = appointmentService;
    this.logger = logger;
  }

  /**
   * Handles POST /appointments - Creates a new appointment
   */
  public async createAppointment(event: IAPIGatewayEvent): Promise<IHTTPResponse> {
    const requestId = event.requestContext.requestId;
    
    try {
      this.logger.info('Processing create appointment request', { 
        requestId,
        method: event.httpMethod,
        path: event.path,
      });

      // Validate request body
      if (!event.body) {
        return this.createErrorResponse(400, 'MISSING_BODY', 'Request body is required', event);
      }

      // Parse request body
      let requestData: CreateAppointmentRequestDTO;
      try {
        requestData = JSON.parse(event.body) as CreateAppointmentRequestDTO;
      } catch (parseError) {
        return this.createErrorResponse(400, 'INVALID_JSON', 'Invalid JSON in request body', event);
      }

      // Validate required fields
      const validationError = this.validateCreateAppointmentRequest(requestData);
      if (validationError) {
        return this.createErrorResponse(400, 'VALIDATION_ERROR', validationError, event);
      }

      // Create appointment
      const result = await this.appointmentService.createAppointment(requestData);

      this.logger.info('Appointment created successfully', {
        requestId,
        appointmentId: result.appointmentId,
        insuredId: requestData.insuredId,
      });

      return this.createSuccessResponse<CreateAppointmentResponseDTO>(201, result, requestId);

    } catch (error) {
      this.logger.error('Error creating appointment', error as Error, { requestId });
      
      return this.createErrorResponse(
        500, 
        'INTERNAL_ERROR', 
        'An internal error occurred while creating the appointment',
        event,
        error as Error
      );
    }
  }

  /**
   * Handles GET /appointments/{insuredId} - Gets appointments by insured ID
   */
  public async getAppointmentsByInsuredId(event: IAPIGatewayEvent): Promise<IHTTPResponse> {
    const requestId = event.requestContext.requestId;
    
    try {
      this.logger.info('Processing get appointments request', { 
        requestId,
        method: event.httpMethod,
        path: event.path,
      });

      // Extract insured ID from path parameters
      const insuredId = event.pathParameters?.insuredId;
      
      if (!insuredId) {
        return this.createErrorResponse(400, 'MISSING_PARAMETER', 'insuredId parameter is required', event);
      }

      // Validate insured ID
      if (typeof insuredId !== 'string' || insuredId.trim().length === 0) {
        return this.createErrorResponse(400, 'INVALID_PARAMETER', 'insuredId must be a non-empty string', event);
      }

      // Get appointments
      const result = await this.appointmentService.getAppointmentsByInsuredId(insuredId.trim());

      this.logger.info('Appointments retrieved successfully', {
        requestId,
        insuredId,
        count: result.count,
      });

      return this.createSuccessResponse<ListAppointmentsResponseDTO>(200, result, requestId);

    } catch (error) {
      this.logger.error('Error retrieving appointments', error as Error, { requestId });
      
      return this.createErrorResponse(
        500, 
        'INTERNAL_ERROR', 
        'An internal error occurred while retrieving appointments',
        event,
        error as Error
      );
    }
  }

  /**
   * Handles SQS confirmation messages - Updates appointment status
   */
  public async processConfirmation(
    appointmentId: string, 
    success: boolean, 
    requestId: string
  ): Promise<void> {
    try {
      this.logger.info('Processing appointment confirmation', { 
        requestId,
        appointmentId,
        success,
      });

      await this.appointmentService.processAppointmentConfirmation(appointmentId, success);

      this.logger.info('Appointment confirmation processed successfully', {
        requestId,
        appointmentId,
        success,
      });

    } catch (error) {
      this.logger.error('Error processing appointment confirmation', error as Error, { 
        requestId,
        appointmentId,
        success,
      });
      throw error; // Re-throw to be handled by the Lambda handler
    }
  }

  /**
   * Validates create appointment request data
   */
  private validateCreateAppointmentRequest(data: CreateAppointmentRequestDTO): string | null {
    if (!data || typeof data !== 'object') {
      return 'Request data must be an object';
    }

    if (!data.insuredId || typeof data.insuredId !== 'string' || data.insuredId.trim().length === 0) {
      return 'insuredId is required and must be a non-empty string';
    }

    if (data.insuredId.length > 50) {
      return 'insuredId must not exceed 50 characters';
    }

    if (!data.scheduleId || typeof data.scheduleId !== 'number' || data.scheduleId <= 0) {
      return 'scheduleId is required and must be a positive number';
    }

    if (data.scheduleId > 999999) {
      return 'scheduleId must not exceed 999999';
    }

    if (!data.countryISO || !['PE', 'CL'].includes(data.countryISO)) {
      return 'countryISO is required and must be either PE or CL';
    }

    return null; // No validation errors
  }

  /**
   * Creates a success HTTP response
   */
  private createSuccessResponse<T>(
    statusCode: number, 
    data: T, 
    requestId: string
  ): IHTTPResponse {
    const response: APIResponseDTO<T> = {
      success: true,
      data,
      timestamp: new Date().toISOString(),
      requestId,
    };

    return {
      statusCode,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,OPTIONS',
        'X-Request-ID': requestId,
      },
      body: JSON.stringify(response),
    };
  }

  /**
   * Creates an error HTTP response
   */
  private createErrorResponse(
    statusCode: number,
    code: string,
    message: string,
    event: IAPIGatewayEvent,
    error?: Error
  ): IHTTPResponse {
    const errorResponse: ErrorResponseDTO = {
      error: {
        code,
        message,
        ...(error && process.env.NODE_ENV !== 'production' && {
          details: {
            name: error.name,
            stack: error.stack,
          },
        }),
      },
      timestamp: new Date().toISOString(),
      path: event.path,
      method: event.httpMethod,
    };

    const response: APIResponseDTO = {
      success: false,
      error: errorResponse.error,
      timestamp: errorResponse.timestamp,
      requestId: event.requestContext.requestId,
    };

    return {
      statusCode,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,OPTIONS',
        'X-Request-ID': event.requestContext.requestId,
      },
      body: JSON.stringify(response),
    };
  }
}
