-- Database initialization script for Rimac Medical Appointments
-- Creates the appointments table with proper indexes and constraints

USE rimac_appointments;

-- Create appointments table
CREATE TABLE IF NOT EXISTS appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    insuredId VARCHAR(50) NOT NULL,
    scheduleId INT NOT NULL,
    countryISO ENUM('PE', 'CL') NOT NULL,
    status ENUM('pending', 'completed', 'failed') NOT NULL DEFAULT 'pending',
    date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for better query performance
    INDEX idx_insuredId (insuredId),
    INDEX idx_countryISO (countryISO),
    INDEX idx_status (status),
    INDEX idx_createdAt (createdAt),
    INDEX idx_composite_country_status (countryISO, status),
    INDEX idx_composite_insured_created (insuredId, createdAt DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data for testing
INSERT INTO appointments (insuredId, scheduleId, countryISO, status, date) VALUES
('00001', 100, 'PE', 'completed', '2024-01-15 10:30:00'),
('00001', 101, 'PE', 'pending', '2024-01-16 14:00:00'),
('00002', 200, 'CL', 'completed', '2024-01-15 11:00:00'),
('00003', 300, 'PE', 'failed', '2024-01-14 09:30:00'),
('00004', 400, 'CL', 'pending', '2024-01-17 16:30:00');

-- Create a view for appointment statistics
CREATE OR REPLACE VIEW appointment_stats AS
SELECT 
    countryISO,
    status,
    COUNT(*) as count,
    DATE(createdAt) as date
FROM appointments 
GROUP BY countryISO, status, DATE(createdAt)
ORDER BY date DESC, countryISO, status;

-- Create a stored procedure for cleanup old appointments (optional)
DELIMITER //
CREATE PROCEDURE CleanupOldAppointments(IN days_old INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    DELETE FROM appointments 
    WHERE createdAt < DATE_SUB(NOW(), INTERVAL days_old DAY)
    AND status IN ('completed', 'failed');
    
    COMMIT;
END //
DELIMITER ;

-- Grant permissions to the application user
GRANT SELECT, INSERT, UPDATE, DELETE ON rimac_appointments.appointments TO 'rimac_user'@'%';
GRANT SELECT ON rimac_appointments.appointment_stats TO 'rimac_user'@'%';
GRANT EXECUTE ON PROCEDURE rimac_appointments.CleanupOldAppointments TO 'rimac_user'@'%';

FLUSH PRIVILEGES;
