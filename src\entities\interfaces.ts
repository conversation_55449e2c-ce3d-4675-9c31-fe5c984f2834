/**
 * Core interfaces for the medical appointments system
 */

import { 
  DynamoDBAppointment, 
  MySQLAppointment, 
  CountryISO, 
  AppointmentStatus 
} from './Appointment';
import { 
  AppointmentSNSMessageDTO, 
  AppointmentEventBridgeEventDTO,
  CreateAppointmentRequestDTO,
  CreateAppointmentResponseDTO,
  ListAppointmentsResponseDTO
} from './DTOs';

/**
 * Repository interfaces following Repository Pattern
 */

export interface IDynamoDBRepository {
  create(appointment: DynamoDBAppointment): Promise<DynamoDBAppointment>;
  findById(appointmentId: string): Promise<DynamoDBAppointment | null>;
  findByInsuredId(insuredId: string): Promise<DynamoDBAppointment[]>;
  update(appointmentId: string, updates: Partial<DynamoDBAppointment>): Promise<DynamoDBAppointment>;
  delete(appointmentId: string): Promise<boolean>;
}

export interface IMySQLRepository {
  create(appointment: MySQLAppointment): Promise<MySQLAppointment>;
  findById(id: number): Promise<MySQLAppointment | null>;
  findByInsuredId(insuredId: string): Promise<MySQLAppointment[]>;
  findByCountry(countryISO: CountryISO): Promise<MySQLAppointment[]>;
  update(id: number, updates: Partial<MySQLAppointment>): Promise<MySQLAppointment>;
  delete(id: number): Promise<boolean>;
}

/**
 * Service interfaces for business logic
 */

export interface IAppointmentService {
  createAppointment(request: CreateAppointmentRequestDTO): Promise<CreateAppointmentResponseDTO>;
  getAppointmentsByInsuredId(insuredId: string): Promise<ListAppointmentsResponseDTO>;
  updateAppointmentStatus(appointmentId: string, status: AppointmentStatus): Promise<void>;
  processAppointmentConfirmation(appointmentId: string, success: boolean): Promise<void>;
}

export interface ISNSService {
  publishAppointmentEvent(message: AppointmentSNSMessageDTO): Promise<string>;
}

export interface ISQSService {
  sendMessage(queueUrl: string, message: Record<string, unknown>): Promise<string>;
  receiveMessages(queueUrl: string, maxMessages?: number): Promise<unknown[]>;
  deleteMessage(queueUrl: string, receiptHandle: string): Promise<void>;
}

export interface IEventBridgeService {
  publishEvent(event: AppointmentEventBridgeEventDTO): Promise<string>;
}

/**
 * Database connection interfaces
 */

export interface IDatabaseConnection {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  isConnected(): boolean;
}

export interface IMySQLConnection extends IDatabaseConnection {
  query<T = unknown>(sql: string, params?: unknown[]): Promise<T[]>;
  execute(sql: string, params?: unknown[]): Promise<{ affectedRows: number; insertId?: number }>;
  beginTransaction(): Promise<void>;
  commit(): Promise<void>;
  rollback(): Promise<void>;
}

/**
 * Logger interface
 */

export interface ILogger {
  info(message: string, meta?: Record<string, unknown>): void;
  error(message: string, error?: Error, meta?: Record<string, unknown>): void;
  warn(message: string, meta?: Record<string, unknown>): void;
  debug(message: string, meta?: Record<string, unknown>): void;
}

/**
 * Configuration interface
 */

export interface IAppConfig {
  aws: {
    region: string;
    dynamodbTableName: string;
    snsTopicArn: string;
    sqsPEQueueUrl: string;
    sqsCLQueueUrl: string;
    sqsConfirmationQueueUrl: string;
    eventBridgeBusName: string;
  };
  database: {
    host: string;
    port: number;
    name: string;
    user: string;
    password: string;
    connectionLimit: number;
  };
  logging: {
    level: string;
  };
  api: {
    version: string;
    corsOrigin: string;
  };
}

/**
 * Lambda event interfaces
 */

export interface ILambdaContext {
  requestId: string;
  functionName: string;
  functionVersion: string;
  memoryLimitInMB: string;
  logGroupName: string;
  logStreamName: string;
  getRemainingTimeInMillis(): number;
}

export interface IAPIGatewayEvent {
  httpMethod: string;
  path: string;
  pathParameters: Record<string, string> | null;
  queryStringParameters: Record<string, string> | null;
  headers: Record<string, string>;
  body: string | null;
  requestContext: {
    requestId: string;
    stage: string;
    httpMethod: string;
    path: string;
  };
}

export interface ISQSEvent {
  Records: Array<{
    messageId: string;
    receiptHandle: string;
    body: string;
    attributes: Record<string, string>;
    messageAttributes: Record<string, unknown>;
    md5OfBody: string;
    eventSource: string;
    eventSourceARN: string;
    awsRegion: string;
  }>;
}

/**
 * HTTP Response interface
 */

export interface IHTTPResponse {
  statusCode: number;
  headers: Record<string, string>;
  body: string;
}

/**
 * Validation interfaces
 */

export interface IValidator<T> {
  validate(data: unknown): { isValid: boolean; errors: string[]; data?: T };
}

export interface IAppointmentValidator extends IValidator<CreateAppointmentRequestDTO> {
  validateInsuredId(insuredId: string): boolean;
  validateScheduleId(scheduleId: number): boolean;
  validateCountryISO(countryISO: string): boolean;
}
