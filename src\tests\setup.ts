/**
 * Jest setup file for global test configuration
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.AWS_REGION = 'us-east-1';
process.env.STAGE = 'test';
process.env.DYNAMODB_APPOINTMENTS_TABLE = 'rimac-appointments-test';
process.env.SNS_TOPIC_ARN = 'arn:aws:sns:us-east-1:123456789012:rimac-appointments-topic-test';
process.env.SQS_PE_QUEUE_URL = 'https://sqs.us-east-1.amazonaws.com/123456789012/rimac-appointments-pe-test';
process.env.SQS_CL_QUEUE_URL = 'https://sqs.us-east-1.amazonaws.com/123456789012/rimac-appointments-cl-test';
process.env.SQS_CONFIRMATION_QUEUE_URL = 'https://sqs.us-east-1.amazonaws.com/123456789012/rimac-appointments-confirmation-test';
process.env.EVENTBRIDGE_BUS_NAME = 'rimac-appointments-bus-test';
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '3306';
process.env.DB_NAME = 'rimac_appointments_test';
process.env.DB_USER = 'test_user';
process.env.DB_PASSWORD = 'test_password';
process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests

// Global test timeout
jest.setTimeout(10000);

// Mock AWS SDK modules globally
jest.mock('@aws-sdk/client-dynamodb');
jest.mock('@aws-sdk/client-sns');
jest.mock('@aws-sdk/client-sqs');
jest.mock('@aws-sdk/client-eventbridge');
jest.mock('@aws-sdk/util-dynamodb');

// Mock UUID generation for consistent testing
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-uuid-1234-5678-9012'),
}));

// Mock Date for consistent timestamps in tests
const mockDate = new Date('2024-01-15T10:30:00.000Z');
global.Date = jest.fn(() => mockDate) as any;
global.Date.UTC = Date.UTC;
global.Date.parse = Date.parse;
global.Date.now = jest.fn(() => mockDate.getTime());

// Global test utilities
global.testUtils = {
  createMockAPIGatewayEvent: (overrides = {}) => ({
    httpMethod: 'POST',
    path: '/appointments',
    pathParameters: null,
    queryStringParameters: null,
    headers: {
      'Content-Type': 'application/json',
    },
    body: null,
    requestContext: {
      requestId: 'test-request-id',
      stage: 'test',
      httpMethod: 'POST',
      path: '/appointments',
    },
    ...overrides,
  }),

  createMockSQSEvent: (records = []) => ({
    Records: records.length > 0 ? records : [
      {
        messageId: 'test-message-id',
        receiptHandle: 'test-receipt-handle',
        body: JSON.stringify({
          appointmentId: 'test-appointment-id',
          insuredId: '00012',
          scheduleId: 100,
          countryISO: 'PE',
          timestamp: '2024-01-15T10:30:00.000Z',
        }),
        attributes: {},
        messageAttributes: {},
        md5OfBody: 'test-md5',
        eventSource: 'aws:sqs',
        eventSourceARN: 'arn:aws:sqs:us-east-1:123456789012:test-queue',
        awsRegion: 'us-east-1',
      },
    ],
  }),

  createMockContext: () => ({
    awsRequestId: 'test-request-id',
    functionName: 'test-function',
    functionVersion: '1',
    memoryLimitInMB: '512',
    logGroupName: '/aws/lambda/test-function',
    logStreamName: 'test-stream',
    getRemainingTimeInMillis: () => 30000,
  }),
};

// Declare global types for TypeScript
declare global {
  namespace NodeJS {
    interface Global {
      testUtils: {
        createMockAPIGatewayEvent: (overrides?: any) => any;
        createMockSQSEvent: (records?: any[]) => any;
        createMockContext: () => any;
      };
    }
  }
}

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Clean up after all tests
afterAll(() => {
  jest.restoreAllMocks();
});
