# AWS Configuration
AWS_REGION=us-east-1
AWS_PROFILE=default

# Stage Configuration
STAGE=dev

# DynamoDB Configuration
DYNAMODB_APPOINTMENTS_TABLE=rimac-appointments-${STAGE}

# MySQL/RDS Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=rimac_appointments
DB_USER=rimac_user
DB_PASSWORD=rimac_password_2024
DB_CONNECTION_LIMIT=10

# SNS Configuration
SNS_TOPIC_ARN=arn:aws:sns:${AWS_REGION}:123456789012:rimac-appointments-topic-${STAGE}

# SQS Configuration
SQS_PE_QUEUE_URL=https://sqs.${AWS_REGION}.amazonaws.com/123456789012/rimac-appointments-pe-${STAGE}
SQS_CL_QUEUE_URL=https://sqs.${AWS_REGION}.amazonaws.com/123456789012/rimac-appointments-cl-${STAGE}
SQS_CONFIRMATION_QUEUE_URL=https://sqs.${AWS_REGION}.amazonaws.com/123456789012/rimac-appointments-confirmation-${STAGE}

# EventBridge Configuration
EVENTBRIDGE_BUS_NAME=rimac-appointments-bus-${STAGE}

# Logging Configuration
LOG_LEVEL=info

# API Configuration
API_VERSION=v1
CORS_ORIGIN=*

# Local Development
IS_OFFLINE=false
