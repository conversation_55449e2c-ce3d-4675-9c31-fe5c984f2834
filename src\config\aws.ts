/**
 * AWS Configuration and Client Setup
 */

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { SNSClient } from '@aws-sdk/client-sns';
import { SQSClient } from '@aws-sdk/client-sqs';
import { EventBridgeClient } from '@aws-sdk/client-eventbridge';

/**
 * AWS Configuration interface
 */
export interface AWSConfig {
  region: string;
  dynamodbTableName: string;
  snsTopicArn: string;
  sqsPEQueueUrl: string;
  sqsCLQueueUrl: string;
  sqsConfirmationQueueUrl: string;
  eventBridgeBusName: string;
}

/**
 * Get AWS configuration from environment variables
 */
export function getAWSConfig(): AWSConfig {
  return {
    region: process.env.AWS_REGION || 'us-east-1',
    dynamodbTableName: process.env.DYNAMODB_APPOINTMENTS_TABLE || 'rimac-appointments-dev',
    snsTopicArn: process.env.SNS_TOPIC_ARN || '',
    sqsPEQueueUrl: process.env.SQS_PE_QUEUE_URL || '',
    sqsCLQueueUrl: process.env.SQS_CL_QUEUE_URL || '',
    sqsConfirmationQueueUrl: process.env.SQS_CONFIRMATION_QUEUE_URL || '',
    eventBridgeBusName: process.env.EVENTBRIDGE_BUS_NAME || 'rimac-appointments-bus-dev',
  };
}

/**
 * AWS Clients Factory
 */
export class AWSClients {
  private static instance: AWSClients;
  private config: AWSConfig;
  
  private _dynamodbClient: DynamoDBClient | null = null;
  private _snsClient: SNSClient | null = null;
  private _sqsClient: SQSClient | null = null;
  private _eventBridgeClient: EventBridgeClient | null = null;

  private constructor() {
    this.config = getAWSConfig();
  }

  /**
   * Singleton pattern to ensure single instance
   */
  public static getInstance(): AWSClients {
    if (!AWSClients.instance) {
      AWSClients.instance = new AWSClients();
    }
    return AWSClients.instance;
  }

  /**
   * Get DynamoDB client
   */
  public getDynamoDBClient(): DynamoDBClient {
    if (!this._dynamodbClient) {
      this._dynamodbClient = new DynamoDBClient({
        region: this.config.region,
        maxAttempts: 3,
        retryMode: 'adaptive',
      });
    }
    return this._dynamodbClient;
  }

  /**
   * Get SNS client
   */
  public getSNSClient(): SNSClient {
    if (!this._snsClient) {
      this._snsClient = new SNSClient({
        region: this.config.region,
        maxAttempts: 3,
        retryMode: 'adaptive',
      });
    }
    return this._snsClient;
  }

  /**
   * Get SQS client
   */
  public getSQSClient(): SQSClient {
    if (!this._sqsClient) {
      this._sqsClient = new SQSClient({
        region: this.config.region,
        maxAttempts: 3,
        retryMode: 'adaptive',
      });
    }
    return this._sqsClient;
  }

  /**
   * Get EventBridge client
   */
  public getEventBridgeClient(): EventBridgeClient {
    if (!this._eventBridgeClient) {
      this._eventBridgeClient = new EventBridgeClient({
        region: this.config.region,
        maxAttempts: 3,
        retryMode: 'adaptive',
      });
    }
    return this._eventBridgeClient;
  }

  /**
   * Get AWS configuration
   */
  public getConfig(): AWSConfig {
    return this.config;
  }

  /**
   * Cleanup all clients
   */
  public async cleanup(): Promise<void> {
    const promises: Promise<void>[] = [];

    if (this._dynamodbClient) {
      promises.push(this._dynamodbClient.destroy());
      this._dynamodbClient = null;
    }

    if (this._snsClient) {
      promises.push(this._snsClient.destroy());
      this._snsClient = null;
    }

    if (this._sqsClient) {
      promises.push(this._sqsClient.destroy());
      this._sqsClient = null;
    }

    if (this._eventBridgeClient) {
      promises.push(this._eventBridgeClient.destroy());
      this._eventBridgeClient = null;
    }

    await Promise.all(promises);
  }
}

/**
 * Helper function to get AWS clients instance
 */
export function getAWSClients(): AWSClients {
  return AWSClients.getInstance();
}

/**
 * Validate AWS configuration
 */
export function validateAWSConfig(config: AWSConfig): void {
  const requiredFields: (keyof AWSConfig)[] = [
    'region',
    'dynamodbTableName',
    'snsTopicArn',
    'sqsPEQueueUrl',
    'sqsCLQueueUrl',
    'sqsConfirmationQueueUrl',
    'eventBridgeBusName',
  ];

  const missingFields = requiredFields.filter(field => !config[field]);

  if (missingFields.length > 0) {
    throw new Error(`Missing required AWS configuration fields: ${missingFields.join(', ')}`);
  }

  // Validate ARN format for SNS
  if (!config.snsTopicArn.startsWith('arn:aws:sns:')) {
    throw new Error('Invalid SNS Topic ARN format');
  }

  // Validate SQS URL format
  const sqsUrlPattern = /^https:\/\/sqs\.[a-z0-9-]+\.amazonaws\.com\/\d+\/.+$/;
  
  if (!sqsUrlPattern.test(config.sqsPEQueueUrl)) {
    throw new Error('Invalid SQS PE Queue URL format');
  }

  if (!sqsUrlPattern.test(config.sqsCLQueueUrl)) {
    throw new Error('Invalid SQS CL Queue URL format');
  }

  if (!sqsUrlPattern.test(config.sqsConfirmationQueueUrl)) {
    throw new Error('Invalid SQS Confirmation Queue URL format');
  }
}
