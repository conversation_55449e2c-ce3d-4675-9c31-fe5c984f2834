/**
 * Error Handler Middleware
 * Centralized error handling for the application
 */

import { APIGatewayProxyResult } from 'aws-lambda';
import { ErrorResponseDTO, APIResponseDTO } from '@/entities/DTOs';
import { ILogger } from '@/entities/interfaces';

/**
 * Custom error classes for different types of errors
 */
export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, code: string = 'INTERNAL_ERROR') {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, field?: string) {
    super(message, 400, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
    
    if (field) {
      this.message = `${field}: ${message}`;
    }
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string) {
    super(`${resource} not found`, 404, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, 'CONFLICT');
    this.name = 'ConflictError';
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401, 'UNAUTHORIZED');
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden') {
    super(message, 403, 'FORBIDDEN');
    this.name = 'ForbiddenError';
  }
}

export class TooManyRequestsError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, 'TOO_MANY_REQUESTS');
    this.name = 'TooManyRequestsError';
  }
}

/**
 * Error Handler Class
 */
export class ErrorHandler {
  private logger: ILogger;

  constructor(logger: ILogger) {
    this.logger = logger;
  }

  /**
   * Handles errors and returns appropriate HTTP response
   */
  public handleError(
    error: Error,
    requestId: string,
    path: string = '',
    method: string = ''
  ): APIGatewayProxyResult {
    // Log the error
    this.logError(error, requestId, path, method);

    // Determine error details
    const errorDetails = this.getErrorDetails(error);

    // Create error response
    const errorResponse: ErrorResponseDTO = {
      error: {
        code: errorDetails.code,
        message: errorDetails.message,
        ...(this.shouldIncludeDetails(error) && {
          details: {
            name: error.name,
            stack: error.stack,
          },
        }),
      },
      timestamp: new Date().toISOString(),
      path,
      method,
    };

    const response: APIResponseDTO = {
      success: false,
      error: errorResponse.error,
      timestamp: errorResponse.timestamp,
      requestId,
    };

    return {
      statusCode: errorDetails.statusCode,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,OPTIONS',
        'X-Request-ID': requestId,
      },
      body: JSON.stringify(response),
    };
  }

  /**
   * Logs error with appropriate level
   */
  private logError(error: Error, requestId: string, path: string, method: string): void {
    const errorContext = {
      requestId,
      path,
      method,
      errorName: error.name,
      errorMessage: error.message,
    };

    if (error instanceof AppError) {
      if (error.statusCode >= 500) {
        this.logger.error('Server error occurred', error, errorContext);
      } else if (error.statusCode >= 400) {
        this.logger.warn('Client error occurred', errorContext);
      } else {
        this.logger.info('Handled error occurred', errorContext);
      }
    } else {
      // Unhandled errors are always logged as errors
      this.logger.error('Unhandled error occurred', error, errorContext);
    }
  }

  /**
   * Extracts error details for response
   */
  private getErrorDetails(error: Error): { statusCode: number; code: string; message: string } {
    if (error instanceof AppError) {
      return {
        statusCode: error.statusCode,
        code: error.code,
        message: error.message,
      };
    }

    // Handle specific AWS SDK errors
    if (error.name === 'ConditionalCheckFailedException') {
      return {
        statusCode: 409,
        code: 'CONFLICT',
        message: 'Resource already exists or condition not met',
      };
    }

    if (error.name === 'ResourceNotFoundException') {
      return {
        statusCode: 404,
        code: 'NOT_FOUND',
        message: 'Resource not found',
      };
    }

    if (error.name === 'ValidationException') {
      return {
        statusCode: 400,
        code: 'VALIDATION_ERROR',
        message: 'Invalid request parameters',
      };
    }

    if (error.name === 'ThrottlingException' || error.name === 'ProvisionedThroughputExceededException') {
      return {
        statusCode: 429,
        code: 'TOO_MANY_REQUESTS',
        message: 'Request rate exceeded',
      };
    }

    if (error.name === 'AccessDeniedException') {
      return {
        statusCode: 403,
        code: 'FORBIDDEN',
        message: 'Access denied',
      };
    }

    // Handle MySQL/Database errors
    if (error.message.includes('ER_DUP_ENTRY')) {
      return {
        statusCode: 409,
        code: 'CONFLICT',
        message: 'Duplicate entry',
      };
    }

    if (error.message.includes('ER_NO_SUCH_TABLE')) {
      return {
        statusCode: 500,
        code: 'DATABASE_ERROR',
        message: 'Database table not found',
      };
    }

    if (error.message.includes('ECONNREFUSED') || error.message.includes('ETIMEDOUT')) {
      return {
        statusCode: 503,
        code: 'SERVICE_UNAVAILABLE',
        message: 'Database connection failed',
      };
    }

    // Default to internal server error
    return {
      statusCode: 500,
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred',
    };
  }

  /**
   * Determines if error details should be included in response
   */
  private shouldIncludeDetails(error: Error): boolean {
    // Only include details in development environment or for operational errors
    return (
      process.env.NODE_ENV !== 'production' ||
      (error instanceof AppError && error.isOperational)
    );
  }
}

/**
 * Global error handler function
 */
export function createErrorHandler(logger: ILogger): ErrorHandler {
  return new ErrorHandler(logger);
}

/**
 * Async wrapper for Lambda handlers with error handling
 */
export function withErrorHandling<T extends unknown[], R>(
  handler: (...args: T) => Promise<R>,
  logger: ILogger
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    try {
      return await handler(...args);
    } catch (error) {
      const errorHandler = createErrorHandler(logger);
      
      // For Lambda handlers, we need to handle the error appropriately
      if (error instanceof AppError) {
        throw error; // Re-throw operational errors
      }
      
      // Log unhandled errors
      logger.error('Unhandled error in Lambda handler', error as Error);
      
      // Re-throw to let Lambda runtime handle it
      throw error;
    }
  };
}

/**
 * Validation helper functions
 */
export function validateRequired(value: unknown, fieldName: string): void {
  if (value === null || value === undefined || value === '') {
    throw new ValidationError(`${fieldName} is required`);
  }
}

export function validateString(value: unknown, fieldName: string, minLength?: number, maxLength?: number): void {
  validateRequired(value, fieldName);
  
  if (typeof value !== 'string') {
    throw new ValidationError(`${fieldName} must be a string`);
  }

  if (minLength !== undefined && value.length < minLength) {
    throw new ValidationError(`${fieldName} must be at least ${minLength} characters long`);
  }

  if (maxLength !== undefined && value.length > maxLength) {
    throw new ValidationError(`${fieldName} must not exceed ${maxLength} characters`);
  }
}

export function validateNumber(value: unknown, fieldName: string, min?: number, max?: number): void {
  validateRequired(value, fieldName);
  
  if (typeof value !== 'number' || isNaN(value)) {
    throw new ValidationError(`${fieldName} must be a valid number`);
  }

  if (min !== undefined && value < min) {
    throw new ValidationError(`${fieldName} must be at least ${min}`);
  }

  if (max !== undefined && value > max) {
    throw new ValidationError(`${fieldName} must not exceed ${max}`);
  }
}

export function validateEnum<T extends string>(value: unknown, fieldName: string, allowedValues: T[]): void {
  validateRequired(value, fieldName);
  
  if (typeof value !== 'string' || !allowedValues.includes(value as T)) {
    throw new ValidationError(`${fieldName} must be one of: ${allowedValues.join(', ')}`);
  }
}
