# Docker Compose for Rimac Medical Appointments Backend
# Provides complete local development environment

version: '3.8'

services:
  # Main application service
  app:
    build:
      context: .
      target: local
    container_name: rimac-appointments-app
    ports:
      - "3002:3000"
      - "3003:3001"
    environment:
      - NODE_ENV=development
      - AWS_REGION=us-east-1
      - STAGE=dev
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=rimac_appointments
      - DB_USER=rimac_user
      - DB_PASSWORD=rimac_password_2024
      - DB_CONNECTION_LIMIT=10
      - LOG_LEVEL=debug
      - IS_OFFLINE=true
    volumes:
      - ./src:/app/src
      - ./serverless.yml:/app/serverless.yml
      - ./package.json:/app/package.json
      - ./tsconfig.json:/app/tsconfig.json
      - node_modules:/app/node_modules
    depends_on:
      mysql:
        condition: service_healthy
      localstack:
        condition: service_healthy
    networks:
      - rimac-network
    command: npm run dev

  # MySQL database service
  mysql:
    image: mysql:8.0
    container_name: rimac-appointments-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root_password_2024
      MYSQL_DATABASE: rimac_appointments
      MYSQL_USER: rimac_user
      MYSQL_PASSWORD: rimac_password_2024
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "rimac_user", "-primac_password_2024"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - rimac-network

  # LocalStack for AWS services emulation
  localstack:
    image: localstack/localstack:2.3
    container_name: rimac-appointments-localstack
    ports:
      - "4566:4566"
    environment:
      - SERVICES=dynamodb,sns,sqs,events
      - DEBUG=0
      - DATA_DIR=/tmp/localstack/data
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
    volumes:
      - localstack_data:/tmp/localstack
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - rimac-network

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: rimac-appointments-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - rimac-network

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: rimac-appointments-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - rimac-network

  # Testing service
  test:
    build:
      context: .
      target: testing
    container_name: rimac-appointments-test
    environment:
      - NODE_ENV=test
    volumes:
      - ./src:/app/src
      - ./coverage:/app/coverage
    networks:
      - rimac-network
    profiles:
      - testing

volumes:
  mysql_data:
    driver: local
  localstack_data:
    driver: local
  redis_data:
    driver: local
  node_modules:
    driver: local

networks:
  rimac-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
