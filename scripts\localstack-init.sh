#!/bin/bash

# LocalStack initialization script for Rimac Medical Appointments
# Creates AWS resources for local development

set -e

echo "🚀 Initializing LocalStack AWS resources for Rimac Medical Appointments..."

# Wait for LocalStack to be ready
echo "⏳ Waiting for LocalStack to be ready..."
until curl -s http://localhost:4566/_localstack/health | grep -q '"dynamodb": "available"'; do
    echo "Waiting for LocalStack services..."
    sleep 2
done

echo "✅ LocalStack is ready!"

# Set AWS CLI configuration for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=us-east-1
export AWS_ENDPOINT_URL=http://localhost:4566

# Create DynamoDB table
echo "📊 Creating DynamoDB table..."
awslocal dynamodb create-table \
    --table-name rimac-appointments-dev \
    --attribute-definitions \
        AttributeName=appointmentId,AttributeType=S \
        AttributeName=insuredId,AttributeType=S \
    --key-schema \
        AttributeName=appointmentId,KeyType=HASH \
    --global-secondary-indexes \
        IndexName=InsuredIdIndex,KeySchema=[{AttributeName=insuredId,KeyType=HASH}],Projection={ProjectionType=ALL},BillingMode=PAY_PER_REQUEST \
    --billing-mode PAY_PER_REQUEST

# Create SNS topic
echo "📢 Creating SNS topic..."
TOPIC_ARN=$(awslocal sns create-topic --name rimac-appointments-topic-dev --output text --query 'TopicArn')
echo "Created SNS topic: $TOPIC_ARN"

# Create SQS queues
echo "📬 Creating SQS queues..."

# Peru queue
PE_QUEUE_URL=$(awslocal sqs create-queue --queue-name rimac-appointments-pe-dev --output text --query 'QueueUrl')
echo "Created Peru queue: $PE_QUEUE_URL"

# Chile queue
CL_QUEUE_URL=$(awslocal sqs create-queue --queue-name rimac-appointments-cl-dev --output text --query 'QueueUrl')
echo "Created Chile queue: $CL_QUEUE_URL"

# Confirmation queue
CONF_QUEUE_URL=$(awslocal sqs create-queue --queue-name rimac-appointments-confirmation-dev --output text --query 'QueueUrl')
echo "Created confirmation queue: $CONF_QUEUE_URL"

# Dead letter queues
PE_DLQ_URL=$(awslocal sqs create-queue --queue-name rimac-appointments-pe-dlq-dev --output text --query 'QueueUrl')
CL_DLQ_URL=$(awslocal sqs create-queue --queue-name rimac-appointments-cl-dlq-dev --output text --query 'QueueUrl')

# Get queue ARNs
PE_QUEUE_ARN=$(awslocal sqs get-queue-attributes --queue-url $PE_QUEUE_URL --attribute-names QueueArn --output text --query 'Attributes.QueueArn')
CL_QUEUE_ARN=$(awslocal sqs get-queue-attributes --queue-url $CL_QUEUE_URL --attribute-names QueueArn --output text --query 'Attributes.QueueArn')

# Create SNS subscriptions with filters
echo "🔗 Creating SNS subscriptions..."

# Peru subscription
awslocal sns subscribe \
    --topic-arn $TOPIC_ARN \
    --protocol sqs \
    --notification-endpoint $PE_QUEUE_ARN \
    --attributes FilterPolicy='{"countryISO":["PE"]}'

# Chile subscription
awslocal sns subscribe \
    --topic-arn $TOPIC_ARN \
    --protocol sqs \
    --notification-endpoint $CL_QUEUE_ARN \
    --attributes FilterPolicy='{"countryISO":["CL"]}'

# Set SQS queue policies to allow SNS
echo "🔐 Setting SQS queue policies..."

PE_POLICY='{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": "*",
      "Action": "sqs:SendMessage",
      "Resource": "'$PE_QUEUE_ARN'",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "'$TOPIC_ARN'"
        }
      }
    }
  ]
}'

CL_POLICY='{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": "*",
      "Action": "sqs:SendMessage",
      "Resource": "'$CL_QUEUE_ARN'",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "'$TOPIC_ARN'"
        }
      }
    }
  ]
}'

awslocal sqs set-queue-attributes --queue-url $PE_QUEUE_URL --attributes Policy="$PE_POLICY"
awslocal sqs set-queue-attributes --queue-url $CL_QUEUE_URL --attributes Policy="$CL_POLICY"

# Create EventBridge custom bus
echo "🎯 Creating EventBridge custom bus..."
awslocal events create-event-bus --name rimac-appointments-bus-dev

# Create EventBridge rule
awslocal events put-rule \
    --event-bus-name rimac-appointments-bus-dev \
    --name rimac-appointments-rule-dev \
    --event-pattern '{"source":["rimac.appointments"],"detail-type":["Appointment Processed"]}' \
    --state ENABLED

# Add target to EventBridge rule
CONF_QUEUE_ARN=$(awslocal sqs get-queue-attributes --queue-url $CONF_QUEUE_URL --attribute-names QueueArn --output text --query 'Attributes.QueueArn')

awslocal events put-targets \
    --event-bus-name rimac-appointments-bus-dev \
    --rule rimac-appointments-rule-dev \
    --targets "Id"="1","Arn"="$CONF_QUEUE_ARN"

# Insert sample data into DynamoDB
echo "📝 Inserting sample data..."
awslocal dynamodb put-item \
    --table-name rimac-appointments-dev \
    --item '{
        "appointmentId": {"S": "sample-appointment-1"},
        "insuredId": {"S": "00001"},
        "scheduleId": {"N": "100"},
        "countryISO": {"S": "PE"},
        "status": {"S": "completed"},
        "createdAt": {"S": "2024-01-15T10:30:00.000Z"},
        "updatedAt": {"S": "2024-01-15T10:35:00.000Z"}
    }'

awslocal dynamodb put-item \
    --table-name rimac-appointments-dev \
    --item '{
        "appointmentId": {"S": "sample-appointment-2"},
        "insuredId": {"S": "00002"},
        "scheduleId": {"N": "200"},
        "countryISO": {"S": "CL"},
        "status": {"S": "pending"},
        "createdAt": {"S": "2024-01-16T14:00:00.000Z"},
        "updatedAt": {"S": "2024-01-16T14:00:00.000Z"}
    }'

echo "✅ LocalStack initialization completed successfully!"
echo ""
echo "📋 Created resources:"
echo "  - DynamoDB table: rimac-appointments-dev"
echo "  - SNS topic: $TOPIC_ARN"
echo "  - SQS queues: PE, CL, Confirmation"
echo "  - EventBridge bus: rimac-appointments-bus-dev"
echo "  - Sample data inserted"
echo ""
echo "🌐 LocalStack endpoints:"
echo "  - Main endpoint: http://localhost:4566"
echo "  - Health check: http://localhost:4566/_localstack/health"
echo ""
echo "🚀 Ready for development!"
