# Rimac Medical Appointments Backend

Sistema de agendamiento de citas médicas para Rimac Seguros implementado con arquitectura serverless en AWS, utilizando Node.js, TypeScript y Serverless Framework.

## 🏗️ Arquitectura

El sistema implementa una arquitectura distribuida y escalable con los siguientes componentes:

### Componentes Principales

- **API Gateway**: Punto de entrada HTTP para las peticiones de la aplicación web
- **Lambda Functions**: 
  - `appointment`: Maneja peticiones HTTP y confirmaciones SQS
  - `appointment_pe`: Procesa citas para Perú
  - `appointment_cl`: Procesa citas para Chile
- **DynamoDB**: Almacenamiento principal con estado de citas
- **MySQL/RDS**: Base de datos por país para citas procesadas
- **SNS**: Publicación de eventos con filtrado por país
- **SQS**: Colas de procesamiento (PE, CL, confirmación)
- **EventBridge**: Orquestación de eventos de confirmación

### Flujo de Procesamiento

```mermaid
graph TD
    A[Web App] --> B[API Gateway]
    B --> C[Lambda appointment]
    C --> D[DynamoDB - pending]
    C --> E[SNS Topic]
    E --> F[SQS PE]
    E --> G[SQS CL]
    F --> H[Lambda appointment_pe]
    G --> I[Lambda appointment_cl]
    H --> J[MySQL PE]
    I --> K[MySQL CL]
    H --> L[EventBridge]
    I --> L
    L --> M[SQS Confirmation]
    M --> C
    C --> N[DynamoDB - completed]
```

## 🚀 Características

- **Clean Architecture**: Separación clara entre capas de presentación, negocio y datos
- **SOLID Principles**: Código mantenible y extensible
- **Repository Pattern**: Abstracción del acceso a datos
- **Error Handling**: Manejo centralizado de errores con logging estructurado
- **Validation**: Validación robusta con Joi
- **Testing**: Cobertura completa con Jest
- **Documentation**: API documentada con OpenAPI/Swagger
- **Monitoring**: Logging estructurado con Winston y CloudWatch

## 📋 Requisitos

- Node.js 18+
- npm 9+
- AWS CLI configurado
- Serverless Framework 3.38+
- Docker y Docker Compose (para desarrollo local)
- MySQL 8.0+ (para desarrollo local)

## 🛠️ Instalación

### 1. Clonar el repositorio

```bash
git clone <repository-url>
cd rimac-medical-appointments-backend
```

### 2. Instalar dependencias

```bash
npm install
```

### 3. Configurar variables de entorno

```bash
cp .env.example .env
# Editar .env con tus configuraciones
```

### 4. Configurar AWS CLI

```bash
aws configure
# Introducir tus credenciales de AWS
```

## 🏃‍♂️ Desarrollo Local

### Opción 1: Con Docker Compose (Recomendado)

```bash
# Iniciar todos los servicios
docker-compose up -d

# Ver logs
docker-compose logs -f app

# Detener servicios
docker-compose down
```

### Opción 2: Desarrollo Nativo

```bash
# Instalar Serverless Framework globalmente
npm install -g serverless

# Iniciar en modo desarrollo
npm run dev

# En otra terminal, iniciar base de datos local
docker-compose up mysql localstack -d
```

## 🚀 Deployment

### Desarrollo

```bash
npm run deploy:dev
```

### Producción

```bash
npm run deploy:prod
```

### Deployment completo con configuración

```bash
# Configurar stage
export STAGE=dev

# Deploy con configuración específica
sls deploy --stage $STAGE --region us-east-1
```

## 📊 Testing

### Ejecutar todas las pruebas

```bash
npm test
```

### Pruebas con cobertura

```bash
npm run test:coverage
```

### Pruebas en modo watch

```bash
npm run test:watch
```

### Pruebas con Docker

```bash
docker-compose --profile testing up test
```

## 📖 API Documentation

La documentación interactiva de la API está disponible en:

- **Local**: http://localhost:3000/docs
- **Desarrollo**: https://dev-api.rimac.com/docs
- **Producción**: https://api.rimac.com/docs

### Endpoints Principales

#### POST /appointments
Crea una nueva cita médica.

```json
{
  "insuredId": "00012",
  "scheduleId": 100,
  "countryISO": "PE"
}
```

**Respuesta:**
```json
{
  "success": true,
  "data": {
    "message": "Appointment request received and is pending",
    "appointmentId": "uuid",
    "status": "pending",
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

#### GET /appointments/{insuredId}
Obtiene todas las citas de un asegurado.

**Respuesta:**
```json
{
  "success": true,
  "data": {
    "appointments": [
      {
        "appointmentId": "uuid",
        "scheduleId": 100,
        "countryISO": "PE",
        "status": "completed",
        "createdAt": "2024-01-15T10:30:00.000Z",
        "updatedAt": "2024-01-15T10:35:00.000Z"
      }
    ],
    "count": 1,
    "insuredId": "00012"
  }
}
```

## 🗂️ Estructura del Proyecto

```
src/
├── controllers/          # Controladores HTTP
├── services/            # Lógica de negocio
├── repositories/        # Acceso a datos
├── entities/           # Entidades y DTOs
├── middleware/         # Middleware y validaciones
├── handlers/           # Handlers de Lambda
├── config/             # Configuraciones
├── utils/              # Utilidades
└── tests/              # Pruebas unitarias

scripts/                # Scripts de inicialización
├── init-db.sql         # Inicialización de MySQL
└── localstack-init.sh  # Configuración de LocalStack

serverless.yml          # Configuración de Serverless
docker-compose.yml      # Configuración de Docker
Dockerfile             # Imagen de Docker
```

## 🔧 Configuración

### Variables de Entorno

| Variable | Descripción | Ejemplo |
|----------|-------------|---------|
| `AWS_REGION` | Región de AWS | `us-east-1` |
| `STAGE` | Entorno de deployment | `dev`, `prod` |
| `DB_HOST` | Host de MySQL | `localhost` |
| `DB_PORT` | Puerto de MySQL | `3306` |
| `DB_NAME` | Nombre de la base de datos | `rimac_appointments` |
| `DB_USER` | Usuario de MySQL | `rimac_user` |
| `DB_PASSWORD` | Contraseña de MySQL | `rimac_password_2024` |
| `LOG_LEVEL` | Nivel de logging | `info`, `debug` |

### Configuración de AWS

El sistema requiere los siguientes permisos de AWS:

- DynamoDB: `Query`, `Scan`, `GetItem`, `PutItem`, `UpdateItem`, `DeleteItem`
- SNS: `Publish`
- SQS: `SendMessage`, `ReceiveMessage`, `DeleteMessage`
- EventBridge: `PutEvents`
- RDS: `DescribeDBInstances`, `DescribeDBClusters`
- CloudWatch: `CreateLogGroup`, `CreateLogStream`, `PutLogEvents`

## 🐛 Debugging

### Logs Locales

```bash
# Ver logs de la aplicación
docker-compose logs -f app

# Ver logs de MySQL
docker-compose logs -f mysql

# Ver logs de LocalStack
docker-compose logs -f localstack
```

### Logs en AWS

```bash
# Ver logs de Lambda
sls logs -f appointment --tail

# Ver logs específicos por país
sls logs -f appointmentPE --tail
sls logs -f appointmentCL --tail
```

### Debugging con VSCode

1. Instalar la extensión "Remote - Containers"
2. Abrir el proyecto en el contenedor
3. Configurar breakpoints
4. Ejecutar en modo debug

## 🔍 Monitoreo

### Métricas Clave

- **Latencia de API**: Tiempo de respuesta de endpoints
- **Tasa de Error**: Porcentaje de requests fallidos
- **Throughput**: Requests por segundo
- **Duración de Lambda**: Tiempo de ejecución de funciones
- **Colas SQS**: Mensajes en cola y DLQ

### Dashboards

- **CloudWatch**: Métricas de AWS
- **X-Ray**: Trazabilidad de requests
- **Custom Metrics**: Métricas de negocio

## 🚨 Troubleshooting

### Problemas Comunes

#### Error de conexión a DynamoDB
```bash
# Verificar configuración de AWS
aws sts get-caller-identity

# Verificar tabla existe
aws dynamodb describe-table --table-name rimac-appointments-dev
```

#### Error de conexión a MySQL
```bash
# Verificar contenedor MySQL
docker-compose ps mysql

# Conectar manualmente
mysql -h localhost -u rimac_user -p rimac_appointments
```

#### Mensajes no llegan a SQS
```bash
# Verificar subscripciones SNS
aws sns list-subscriptions-by-topic --topic-arn <topic-arn>

# Verificar filtros de mensajes
aws sns get-subscription-attributes --subscription-arn <subscription-arn>
```

### Logs de Error

Los errores se categorizan en:

- **4xx**: Errores de cliente (validación, autorización)
- **5xx**: Errores de servidor (base de datos, servicios externos)
- **Timeout**: Errores de tiempo de espera
- **Throttling**: Errores de límite de tasa

## 🤝 Contribución

1. Fork el proyecto
2. Crear una rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit los cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear un Pull Request

### Estándares de Código

- **ESLint**: Linting automático
- **Prettier**: Formateo de código
- **Husky**: Git hooks para calidad
- **Conventional Commits**: Formato de commits

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo [LICENSE](LICENSE) para más detalles.

## 👥 Equipo

- **Arquitecto de Software**: Diseño de la arquitectura serverless
- **Desarrollador Backend**: Implementación de APIs y servicios
- **DevOps Engineer**: Configuración de CI/CD y monitoreo
- **QA Engineer**: Pruebas automatizadas y manuales

## 📞 Soporte

Para soporte técnico:

- **Email**: <EMAIL>
- **Slack**: #rimac-appointments
- **Jira**: Proyecto RIMA

---

**Rimac Seguros** - Sistema de Agendamiento de Citas Médicas  
Versión 1.0.0 - Enero 2024
