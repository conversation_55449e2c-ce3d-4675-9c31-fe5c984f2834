/**
 * Validation Middleware
 * Provides request validation using Joi schemas
 */

import Jo<PERSON> from 'joi';
import { CreateAppointmentRequestDTO } from '@/entities/DTOs';
import { IValidator, IAppointmentValidator } from '@/entities/interfaces';
import { ValidationError } from './errorHandler';

/**
 * Generic Joi-based validator
 */
export class JoiValidator<T> implements IValidator<T> {
  private schema: Joi.ObjectSchema;

  constructor(schema: Joi.ObjectSchema) {
    this.schema = schema;
  }

  public validate(data: unknown): { isValid: boolean; errors: string[]; data?: T } {
    const result = this.schema.validate(data, {
      abortEarly: false,
      stripUnknown: true,
      convert: true,
    });

    if (result.error) {
      const errors = result.error.details.map(detail => detail.message);
      return {
        isValid: false,
        errors,
      };
    }

    return {
      isValid: true,
      errors: [],
      data: result.value as T,
    };
  }
}

/**
 * Appointment-specific validator
 */
export class AppointmentValidator extends JoiValidator<CreateAppointmentRequestDTO> implements IAppointmentValidator {
  constructor() {
    const schema = Joi.object({
      insuredId: Joi.string()
        .trim()
        .min(1)
        .max(50)
        .required()
        .messages({
          'string.empty': 'insuredId cannot be empty',
          'string.min': 'insuredId must be at least 1 character long',
          'string.max': 'insuredId must not exceed 50 characters',
          'any.required': 'insuredId is required',
        }),
      
      scheduleId: Joi.number()
        .integer()
        .positive()
        .max(999999)
        .required()
        .messages({
          'number.base': 'scheduleId must be a number',
          'number.integer': 'scheduleId must be an integer',
          'number.positive': 'scheduleId must be a positive number',
          'number.max': 'scheduleId must not exceed 999999',
          'any.required': 'scheduleId is required',
        }),
      
      countryISO: Joi.string()
        .valid('PE', 'CL')
        .required()
        .messages({
          'any.only': 'countryISO must be either PE or CL',
          'any.required': 'countryISO is required',
        }),
    });

    super(schema);
  }

  public validateInsuredId(insuredId: string): boolean {
    const schema = Joi.string().trim().min(1).max(50);
    const result = schema.validate(insuredId);
    return !result.error;
  }

  public validateScheduleId(scheduleId: number): boolean {
    const schema = Joi.number().integer().positive().max(999999);
    const result = schema.validate(scheduleId);
    return !result.error;
  }

  public validateCountryISO(countryISO: string): boolean {
    const schema = Joi.string().valid('PE', 'CL');
    const result = schema.validate(countryISO);
    return !result.error;
  }
}

/**
 * Path parameter validator
 */
export class PathParameterValidator {
  public static validateInsuredId(insuredId: string | null | undefined): string {
    if (!insuredId) {
      throw new ValidationError('insuredId path parameter is required');
    }

    const schema = Joi.string().trim().min(1).max(50);
    const result = schema.validate(insuredId);

    if (result.error) {
      throw new ValidationError(`Invalid insuredId: ${result.error.message}`);
    }

    return result.value as string;
  }

  public static validateAppointmentId(appointmentId: string | null | undefined): string {
    if (!appointmentId) {
      throw new ValidationError('appointmentId path parameter is required');
    }

    const schema = Joi.string().uuid();
    const result = schema.validate(appointmentId);

    if (result.error) {
      throw new ValidationError(`Invalid appointmentId: ${result.error.message}`);
    }

    return result.value as string;
  }
}

/**
 * Query parameter validator
 */
export class QueryParameterValidator {
  public static validateLimit(limit: string | null | undefined): number {
    if (!limit) {
      return 10; // Default limit
    }

    const schema = Joi.number().integer().min(1).max(100);
    const result = schema.validate(parseInt(limit, 10));

    if (result.error) {
      throw new ValidationError(`Invalid limit: ${result.error.message}`);
    }

    return result.value as number;
  }

  public static validateOffset(offset: string | null | undefined): number {
    if (!offset) {
      return 0; // Default offset
    }

    const schema = Joi.number().integer().min(0);
    const result = schema.validate(parseInt(offset, 10));

    if (result.error) {
      throw new ValidationError(`Invalid offset: ${result.error.message}`);
    }

    return result.value as number;
  }

  public static validateStatus(status: string | null | undefined): string | undefined {
    if (!status) {
      return undefined;
    }

    const schema = Joi.string().valid('pending', 'completed', 'failed');
    const result = schema.validate(status);

    if (result.error) {
      throw new ValidationError(`Invalid status: ${result.error.message}`);
    }

    return result.value as string;
  }
}

/**
 * Request body validator
 */
export class RequestBodyValidator {
  public static validateJSON(body: string | null): unknown {
    if (!body) {
      throw new ValidationError('Request body is required');
    }

    try {
      return JSON.parse(body);
    } catch (error) {
      throw new ValidationError('Invalid JSON in request body');
    }
  }

  public static validateCreateAppointment(body: string | null): CreateAppointmentRequestDTO {
    const data = this.validateJSON(body);
    
    const validator = new AppointmentValidator();
    const result = validator.validate(data);

    if (!result.isValid) {
      throw new ValidationError(`Validation failed: ${result.errors.join(', ')}`);
    }

    return result.data!;
  }
}

/**
 * Header validator
 */
export class HeaderValidator {
  public static validateContentType(headers: Record<string, string>): void {
    const contentType = headers['content-type'] || headers['Content-Type'];
    
    if (!contentType) {
      throw new ValidationError('Content-Type header is required');
    }

    if (!contentType.includes('application/json')) {
      throw new ValidationError('Content-Type must be application/json');
    }
  }

  public static validateAuthorization(headers: Record<string, string>): string | undefined {
    const authorization = headers['authorization'] || headers['Authorization'];
    
    if (!authorization) {
      return undefined; // Authorization is optional for this API
    }

    const schema = Joi.string().pattern(/^Bearer .+$/);
    const result = schema.validate(authorization);

    if (result.error) {
      throw new ValidationError('Invalid Authorization header format. Expected: Bearer <token>');
    }

    return result.value as string;
  }
}

/**
 * Validation middleware factory
 */
export function createValidationMiddleware() {
  return {
    appointmentValidator: new AppointmentValidator(),
    pathValidator: PathParameterValidator,
    queryValidator: QueryParameterValidator,
    bodyValidator: RequestBodyValidator,
    headerValidator: HeaderValidator,
  };
}

/**
 * Validation helper functions
 */
export function validateAndTransform<T>(
  data: unknown,
  validator: IValidator<T>
): T {
  const result = validator.validate(data);
  
  if (!result.isValid) {
    throw new ValidationError(`Validation failed: ${result.errors.join(', ')}`);
  }

  return result.data!;
}

export function validateRequired<T>(value: T | null | undefined, fieldName: string): T {
  if (value === null || value === undefined) {
    throw new ValidationError(`${fieldName} is required`);
  }
  return value;
}

export function validateOptional<T>(
  value: T | null | undefined,
  validator: (val: T) => boolean,
  fieldName: string
): T | undefined {
  if (value === null || value === undefined) {
    return undefined;
  }

  if (!validator(value)) {
    throw new ValidationError(`Invalid ${fieldName}`);
  }

  return value;
}
