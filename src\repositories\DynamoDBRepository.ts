/**
 * DynamoDB Repository Implementation
 * Implements the Repository Pattern for DynamoDB operations
 */

import { 
  DynamoDBClient, 
  GetItemCommand, 
  PutItemCommand, 
  UpdateItemCommand, 
  DeleteItemCommand,
  QueryCommand,
  ScanCommand
} from '@aws-sdk/client-dynamodb';
import { marshall, unmarshall } from '@aws-sdk/util-dynamodb';
import { DynamoDBAppointment } from '@/entities/Appointment';
import { IDynamoDBRepository, ILogger } from '@/entities/interfaces';

export class DynamoDBRepository implements IDynamoDBRepository {
  private client: DynamoDBClient;
  private tableName: string;
  private logger: ILogger;

  constructor(client: DynamoDBClient, tableName: string, logger: ILogger) {
    this.client = client;
    this.tableName = tableName;
    this.logger = logger;
  }

  /**
   * Creates a new appointment in DynamoDB
   */
  public async create(appointment: DynamoDBAppointment): Promise<DynamoDBAppointment> {
    try {
      this.logger.info('Creating appointment in DynamoDB', { 
        appointmentId: appointment.appointmentId,
        insuredId: appointment.insuredId 
      });

      const command = new PutItemCommand({
        TableName: this.tableName,
        Item: marshall(appointment),
        ConditionExpression: 'attribute_not_exists(appointmentId)',
      });

      await this.client.send(command);

      this.logger.info('Appointment created successfully in DynamoDB', { 
        appointmentId: appointment.appointmentId 
      });

      return appointment;
    } catch (error) {
      this.logger.error('Error creating appointment in DynamoDB', error as Error, {
        appointmentId: appointment.appointmentId,
        insuredId: appointment.insuredId
      });
      throw new Error(`Failed to create appointment: ${(error as Error).message}`);
    }
  }

  /**
   * Finds an appointment by ID
   */
  public async findById(appointmentId: string): Promise<DynamoDBAppointment | null> {
    try {
      this.logger.debug('Finding appointment by ID in DynamoDB', { appointmentId });

      const command = new GetItemCommand({
        TableName: this.tableName,
        Key: marshall({ appointmentId }),
      });

      const result = await this.client.send(command);

      if (!result.Item) {
        this.logger.debug('Appointment not found in DynamoDB', { appointmentId });
        return null;
      }

      const appointment = unmarshall(result.Item) as DynamoDBAppointment;
      
      this.logger.debug('Appointment found in DynamoDB', { 
        appointmentId,
        status: appointment.status 
      });

      return appointment;
    } catch (error) {
      this.logger.error('Error finding appointment by ID in DynamoDB', error as Error, {
        appointmentId
      });
      throw new Error(`Failed to find appointment: ${(error as Error).message}`);
    }
  }

  /**
   * Finds all appointments for a specific insured ID
   */
  public async findByInsuredId(insuredId: string): Promise<DynamoDBAppointment[]> {
    try {
      this.logger.debug('Finding appointments by insured ID in DynamoDB', { insuredId });

      const command = new QueryCommand({
        TableName: this.tableName,
        IndexName: 'InsuredIdIndex',
        KeyConditionExpression: 'insuredId = :insuredId',
        ExpressionAttributeValues: marshall({
          ':insuredId': insuredId,
        }),
      });

      const result = await this.client.send(command);

      if (!result.Items || result.Items.length === 0) {
        this.logger.debug('No appointments found for insured ID in DynamoDB', { insuredId });
        return [];
      }

      const appointments = result.Items.map(item => unmarshall(item) as DynamoDBAppointment);
      
      this.logger.debug('Appointments found for insured ID in DynamoDB', { 
        insuredId,
        count: appointments.length 
      });

      return appointments;
    } catch (error) {
      this.logger.error('Error finding appointments by insured ID in DynamoDB', error as Error, {
        insuredId
      });
      throw new Error(`Failed to find appointments for insured ID: ${(error as Error).message}`);
    }
  }

  /**
   * Updates an appointment
   */
  public async update(
    appointmentId: string, 
    updates: Partial<DynamoDBAppointment>
  ): Promise<DynamoDBAppointment> {
    try {
      this.logger.info('Updating appointment in DynamoDB', { 
        appointmentId,
        updates: Object.keys(updates) 
      });

      // Build update expression dynamically
      const updateExpressions: string[] = [];
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributeValues: Record<string, unknown> = {};

      Object.entries(updates).forEach(([key, value], index) => {
        if (key !== 'appointmentId') { // Don't update the primary key
          const nameKey = `#attr${index}`;
          const valueKey = `:val${index}`;
          
          updateExpressions.push(`${nameKey} = ${valueKey}`);
          expressionAttributeNames[nameKey] = key;
          expressionAttributeValues[valueKey] = value;
        }
      });

      // Always update the updatedAt timestamp
      const timestampKey = `#updatedAt`;
      const timestampValue = `:updatedAt`;
      updateExpressions.push(`${timestampKey} = ${timestampValue}`);
      expressionAttributeNames[timestampKey] = 'updatedAt';
      expressionAttributeValues[timestampValue] = new Date().toISOString();

      const command = new UpdateItemCommand({
        TableName: this.tableName,
        Key: marshall({ appointmentId }),
        UpdateExpression: `SET ${updateExpressions.join(', ')}`,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: marshall(expressionAttributeValues),
        ReturnValues: 'ALL_NEW',
      });

      const result = await this.client.send(command);

      if (!result.Attributes) {
        throw new Error('No attributes returned from update operation');
      }

      const updatedAppointment = unmarshall(result.Attributes) as DynamoDBAppointment;

      this.logger.info('Appointment updated successfully in DynamoDB', { 
        appointmentId,
        newStatus: updatedAppointment.status 
      });

      return updatedAppointment;
    } catch (error) {
      this.logger.error('Error updating appointment in DynamoDB', error as Error, {
        appointmentId,
        updates
      });
      throw new Error(`Failed to update appointment: ${(error as Error).message}`);
    }
  }

  /**
   * Deletes an appointment
   */
  public async delete(appointmentId: string): Promise<boolean> {
    try {
      this.logger.info('Deleting appointment from DynamoDB', { appointmentId });

      const command = new DeleteItemCommand({
        TableName: this.tableName,
        Key: marshall({ appointmentId }),
        ConditionExpression: 'attribute_exists(appointmentId)',
      });

      await this.client.send(command);

      this.logger.info('Appointment deleted successfully from DynamoDB', { appointmentId });

      return true;
    } catch (error) {
      this.logger.error('Error deleting appointment from DynamoDB', error as Error, {
        appointmentId
      });
      
      if ((error as Error).name === 'ConditionalCheckFailedException') {
        return false; // Appointment doesn't exist
      }
      
      throw new Error(`Failed to delete appointment: ${(error as Error).message}`);
    }
  }

  /**
   * Health check method to verify DynamoDB connectivity
   */
  public async healthCheck(): Promise<boolean> {
    try {
      const command = new ScanCommand({
        TableName: this.tableName,
        Limit: 1,
      });

      await this.client.send(command);
      return true;
    } catch (error) {
      this.logger.error('DynamoDB health check failed', error as Error);
      return false;
    }
  }
}
