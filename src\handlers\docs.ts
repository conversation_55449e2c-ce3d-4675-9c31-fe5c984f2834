/**
 * Documentation Handler - Serves OpenAPI/Swagger documentation
 * Provides interactive API documentation at /docs endpoint
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { createRequestLogger } from '@/utils/logger';

/**
 * OpenAPI 3.0 specification for the Medical Appointments API
 */
const openApiSpec = {
  openapi: '3.0.3',
  info: {
    title: 'Rimac Medical Appointments API',
    description: `
# Rimac Medical Appointments Backend API

Sistema de agendamiento de citas médicas para Rimac Seguros que maneja el flujo completo de procesamiento de citas médicas por país (Perú y Chile).

## Arquitectura

El sistema utiliza una arquitectura serverless en AWS con los siguientes componentes:

- **API Gateway**: Punto de entrada para las peticiones HTTP
- **Lambda Functions**: Procesamiento de la lógica de negocio
- **DynamoDB**: Almacenamiento principal de citas con estado
- **MySQL/RDS**: Almacenamiento por país de citas procesadas
- **SNS**: Publicación de eventos con filtrado por país
- **SQS**: Colas de procesamiento por país y confirmación
- **EventBridge**: Orquestación de eventos de confirmación

## Flujo de Procesamiento

1. **Creación de Cita**: POST /appointments crea una cita en DynamoDB con estado "pending"
2. **Publicación SNS**: Se publica un evento a SNS con filtros por país
3. **Procesamiento por País**: Las colas SQS específicas procesan las citas en MySQL
4. **Confirmación**: EventBridge envía confirmaciones que actualizan el estado en DynamoDB

## Principios de Diseño

- **Clean Architecture**: Separación clara entre capas de presentación, negocio y datos
- **SOLID Principles**: Código mantenible y extensible
- **Repository Pattern**: Abstracción del acceso a datos
- **Error Handling**: Manejo centralizado de errores con logging estructurado
    `,
    version: '1.0.0',
    contact: {
      name: 'Rimac Seguros - Medical Appointments Team',
      email: '<EMAIL>',
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT',
    },
  },
  servers: [
    {
      url: 'https://api.rimac.com/v1',
      description: 'Production server',
    },
    {
      url: 'https://dev-api.rimac.com/v1',
      description: 'Development server',
    },
    {
      url: 'http://localhost:3000',
      description: 'Local development server',
    },
  ],
  paths: {
    '/appointments': {
      post: {
        summary: 'Create a new medical appointment',
        description: 'Creates a new medical appointment request. The appointment is initially stored with "pending" status and processed asynchronously.',
        operationId: 'createAppointment',
        tags: ['Appointments'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/CreateAppointmentRequest',
              },
              examples: {
                peru: {
                  summary: 'Peru appointment example',
                  value: {
                    insuredId: '00012',
                    scheduleId: 100,
                    countryISO: 'PE',
                  },
                },
                chile: {
                  summary: 'Chile appointment example',
                  value: {
                    insuredId: '00034',
                    scheduleId: 200,
                    countryISO: 'CL',
                  },
                },
              },
            },
          },
        },
        responses: {
          '201': {
            description: 'Appointment created successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/CreateAppointmentResponse',
                },
                example: {
                  success: true,
                  data: {
                    message: 'Appointment request received and is pending',
                    appointmentId: '123e4567-e89b-12d3-a456-************',
                    status: 'pending',
                    timestamp: '2024-01-15T10:30:00.000Z',
                  },
                  timestamp: '2024-01-15T10:30:00.000Z',
                  requestId: 'req-123456',
                },
              },
            },
          },
          '400': {
            description: 'Bad request - Invalid input data',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
                example: {
                  success: false,
                  error: {
                    code: 'VALIDATION_ERROR',
                    message: 'insuredId is required and must be a non-empty string',
                  },
                  timestamp: '2024-01-15T10:30:00.000Z',
                  requestId: 'req-123456',
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
        },
      },
    },
    '/appointments/{insuredId}': {
      get: {
        summary: 'Get appointments by insured ID',
        description: 'Retrieves all appointments for a specific insured person, ordered by creation date (newest first).',
        operationId: 'getAppointmentsByInsuredId',
        tags: ['Appointments'],
        parameters: [
          {
            name: 'insuredId',
            in: 'path',
            required: true,
            description: 'The ID of the insured person',
            schema: {
              type: 'string',
              minLength: 1,
              maxLength: 50,
              example: '00012',
            },
          },
        ],
        responses: {
          '200': {
            description: 'Appointments retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ListAppointmentsResponse',
                },
                example: {
                  success: true,
                  data: {
                    appointments: [
                      {
                        appointmentId: '123e4567-e89b-12d3-a456-************',
                        scheduleId: 100,
                        countryISO: 'PE',
                        status: 'completed',
                        createdAt: '2024-01-15T10:30:00.000Z',
                        updatedAt: '2024-01-15T10:35:00.000Z',
                      },
                    ],
                    count: 1,
                    insuredId: '00012',
                  },
                  timestamp: '2024-01-15T10:30:00.000Z',
                  requestId: 'req-123456',
                },
              },
            },
          },
          '400': {
            description: 'Bad request - Invalid insured ID',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
        },
      },
    },
  },
  components: {
    schemas: {
      CreateAppointmentRequest: {
        type: 'object',
        required: ['insuredId', 'scheduleId', 'countryISO'],
        properties: {
          insuredId: {
            type: 'string',
            minLength: 1,
            maxLength: 50,
            description: 'Unique identifier for the insured person',
            example: '00012',
          },
          scheduleId: {
            type: 'integer',
            minimum: 1,
            maximum: 999999,
            description: 'Schedule identifier for the appointment slot',
            example: 100,
          },
          countryISO: {
            type: 'string',
            enum: ['PE', 'CL'],
            description: 'Country ISO code (PE for Peru, CL for Chile)',
            example: 'PE',
          },
        },
      },
      CreateAppointmentResponse: {
        type: 'object',
        properties: {
          message: {
            type: 'string',
            example: 'Appointment request received and is pending',
          },
          appointmentId: {
            type: 'string',
            format: 'uuid',
            description: 'Unique identifier for the created appointment',
            example: '123e4567-e89b-12d3-a456-************',
          },
          status: {
            type: 'string',
            enum: ['pending'],
            example: 'pending',
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            example: '2024-01-15T10:30:00.000Z',
          },
        },
      },
      AppointmentResponse: {
        type: 'object',
        properties: {
          appointmentId: {
            type: 'string',
            format: 'uuid',
            example: '123e4567-e89b-12d3-a456-************',
          },
          scheduleId: {
            type: 'integer',
            example: 100,
          },
          countryISO: {
            type: 'string',
            enum: ['PE', 'CL'],
            example: 'PE',
          },
          status: {
            type: 'string',
            enum: ['pending', 'completed', 'failed'],
            example: 'completed',
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            example: '2024-01-15T10:30:00.000Z',
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            example: '2024-01-15T10:35:00.000Z',
          },
        },
      },
      ListAppointmentsResponse: {
        type: 'object',
        properties: {
          appointments: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/AppointmentResponse',
            },
          },
          count: {
            type: 'integer',
            description: 'Total number of appointments',
            example: 1,
          },
          insuredId: {
            type: 'string',
            example: '00012',
          },
        },
      },
      APIResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: true,
          },
          data: {
            type: 'object',
            description: 'Response data (varies by endpoint)',
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            example: '2024-01-15T10:30:00.000Z',
          },
          requestId: {
            type: 'string',
            example: 'req-123456',
          },
        },
      },
      ErrorResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: false,
          },
          error: {
            type: 'object',
            properties: {
              code: {
                type: 'string',
                example: 'VALIDATION_ERROR',
              },
              message: {
                type: 'string',
                example: 'Invalid request parameters',
              },
              details: {
                type: 'object',
                description: 'Additional error details (only in development)',
              },
            },
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            example: '2024-01-15T10:30:00.000Z',
          },
          requestId: {
            type: 'string',
            example: 'req-123456',
          },
        },
      },
    },
  },
  tags: [
    {
      name: 'Appointments',
      description: 'Medical appointment management operations',
    },
  ],
};

/**
 * Swagger UI HTML template
 */
const swaggerUIHTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rimac Medical Appointments API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
        *, *:before, *:after { box-sizing: inherit; }
        body { margin:0; background: #fafafa; }
        .swagger-ui .topbar { display: none; }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: window.location.origin + window.location.pathname.replace('/docs', '') + '/docs/openapi.json',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                tryItOutEnabled: true,
                supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
                onComplete: function() {
                    console.log('Swagger UI loaded successfully');
                }
            });
        };
    </script>
</body>
</html>
`;

/**
 * Main documentation handler
 */
export const handler = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  const logger = createRequestLogger(context.awsRequestId, context.functionName);

  try {
    logger.info('Documentation request received', {
      path: event.path,
      method: event.httpMethod,
    });

    // Handle different documentation routes
    if (event.path.endsWith('/openapi.json') || event.path.endsWith('/swagger.json')) {
      // Serve OpenAPI JSON specification
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Cache-Control': 'public, max-age=3600',
        },
        body: JSON.stringify(openApiSpec, null, 2),
      };
    } else {
      // Serve Swagger UI HTML
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'text/html',
          'Access-Control-Allow-Origin': '*',
          'Cache-Control': 'public, max-age=3600',
        },
        body: swaggerUIHTML,
      };
    }
  } catch (error) {
    logger.error('Error serving documentation', error as Error);

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to serve documentation',
        },
        timestamp: new Date().toISOString(),
        requestId: context.awsRequestId,
      }),
    };
  }
};
