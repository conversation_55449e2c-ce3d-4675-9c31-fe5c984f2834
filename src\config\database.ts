/**
 * Database Configuration and Connection Management
 */

import mysql from 'mysql2/promise';
import { IMySQLConnection, ILogger } from '@/entities/interfaces';

export class MySQLConnection implements IMySQLConnection {
  private pool: mysql.Pool | null = null;
  private logger: ILogger;
  private config: mysql.PoolOptions;

  constructor(config: mysql.PoolOptions, logger: ILogger) {
    this.config = config;
    this.logger = logger;
  }

  /**
   * Establishes connection to MySQL database
   */
  public async connect(): Promise<void> {
    try {
      this.logger.info('Connecting to MySQL database', {
        host: this.config.host,
        port: this.config.port,
        database: this.config.database
      });

      this.pool = mysql.createPool({
        ...this.config,
        waitForConnections: true,
        queueLimit: 0,
        acquireTimeout: 60000,
        timeout: 60000,
        reconnect: true,
      });

      // Test the connection
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();

      this.logger.info('Successfully connected to MySQL database');
    } catch (error) {
      this.logger.error('Failed to connect to MySQL database', error as Error);
      throw new Error(`MySQL connection failed: ${(error as Error).message}`);
    }
  }

  /**
   * Closes the database connection
   */
  public async disconnect(): Promise<void> {
    try {
      if (this.pool) {
        this.logger.info('Disconnecting from MySQL database');
        await this.pool.end();
        this.pool = null;
        this.logger.info('Successfully disconnected from MySQL database');
      }
    } catch (error) {
      this.logger.error('Error disconnecting from MySQL database', error as Error);
      throw new Error(`MySQL disconnection failed: ${(error as Error).message}`);
    }
  }

  /**
   * Checks if the database connection is active
   */
  public isConnected(): boolean {
    return this.pool !== null;
  }

  /**
   * Executes a query and returns results
   */
  public async query<T = unknown>(sql: string, params?: unknown[]): Promise<T[]> {
    if (!this.pool) {
      throw new Error('Database connection not established');
    }

    try {
      this.logger.debug('Executing MySQL query', { sql, paramsCount: params?.length || 0 });

      const [rows] = await this.pool.execute(sql, params || []);
      
      this.logger.debug('MySQL query executed successfully', { 
        rowCount: Array.isArray(rows) ? rows.length : 0 
      });

      return rows as T[];
    } catch (error) {
      this.logger.error('MySQL query execution failed', error as Error, { sql });
      throw new Error(`Query execution failed: ${(error as Error).message}`);
    }
  }

  /**
   * Executes a statement and returns execution result
   */
  public async execute(sql: string, params?: unknown[]): Promise<{ affectedRows: number; insertId?: number }> {
    if (!this.pool) {
      throw new Error('Database connection not established');
    }

    try {
      this.logger.debug('Executing MySQL statement', { sql, paramsCount: params?.length || 0 });

      const [result] = await this.pool.execute(sql, params || []);
      
      const executionResult = result as mysql.ResultSetHeader;
      
      this.logger.debug('MySQL statement executed successfully', { 
        affectedRows: executionResult.affectedRows,
        insertId: executionResult.insertId 
      });

      return {
        affectedRows: executionResult.affectedRows,
        insertId: executionResult.insertId,
      };
    } catch (error) {
      this.logger.error('MySQL statement execution failed', error as Error, { sql });
      throw new Error(`Statement execution failed: ${(error as Error).message}`);
    }
  }

  /**
   * Begins a database transaction
   */
  public async beginTransaction(): Promise<void> {
    if (!this.pool) {
      throw new Error('Database connection not established');
    }

    try {
      this.logger.debug('Beginning MySQL transaction');
      await this.pool.execute('START TRANSACTION');
      this.logger.debug('MySQL transaction started successfully');
    } catch (error) {
      this.logger.error('Failed to begin MySQL transaction', error as Error);
      throw new Error(`Transaction begin failed: ${(error as Error).message}`);
    }
  }

  /**
   * Commits the current transaction
   */
  public async commit(): Promise<void> {
    if (!this.pool) {
      throw new Error('Database connection not established');
    }

    try {
      this.logger.debug('Committing MySQL transaction');
      await this.pool.execute('COMMIT');
      this.logger.debug('MySQL transaction committed successfully');
    } catch (error) {
      this.logger.error('Failed to commit MySQL transaction', error as Error);
      throw new Error(`Transaction commit failed: ${(error as Error).message}`);
    }
  }

  /**
   * Rolls back the current transaction
   */
  public async rollback(): Promise<void> {
    if (!this.pool) {
      throw new Error('Database connection not established');
    }

    try {
      this.logger.debug('Rolling back MySQL transaction');
      await this.pool.execute('ROLLBACK');
      this.logger.debug('MySQL transaction rolled back successfully');
    } catch (error) {
      this.logger.error('Failed to rollback MySQL transaction', error as Error);
      throw new Error(`Transaction rollback failed: ${(error as Error).message}`);
    }
  }
}

/**
 * Factory function to create MySQL connection
 */
export function createMySQLConnection(logger: ILogger): MySQLConnection {
  const config: mysql.PoolOptions = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306', 10),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'rimac_appointments',
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '10', 10),
    charset: 'utf8mb4',
    timezone: '+00:00',
    supportBigNumbers: true,
    bigNumberStrings: true,
  };

  return new MySQLConnection(config, logger);
}
