/**
 * SQS Service - Handles Amazon SQS operations
 * Manages message sending, receiving, and deletion for appointment processing
 */

import { 
  SQSClient, 
  SendMessageCommand, 
  ReceiveMessageCommand, 
  DeleteMessageCommand,
  GetQueueAttributesCommand,
} from '@aws-sdk/client-sqs';
import { ISQSService, ILogger } from '@/entities/interfaces';

export class SQSService implements ISQSService {
  private sqsClient: SQSClient;
  private logger: ILogger;

  constructor(sqsClient: SQSClient, logger: ILogger) {
    this.sqsClient = sqsClient;
    this.logger = logger;
  }

  /**
   * Sends a message to the specified SQS queue
   */
  public async sendMessage(queueUrl: string, message: Record<string, unknown>): Promise<string> {
    try {
      this.logger.info('Sending message to SQS queue', {
        queueUrl,
        messageKeys: Object.keys(message),
      });

      // Validate inputs
      this.validateQueueUrl(queueUrl);
      this.validateMessage(message);

      // Prepare message body
      const messageBody = JSON.stringify(message);

      // Create send message command
      const command = new SendMessageCommand({
        QueueUrl: queueUrl,
        MessageBody: messageBody,
        MessageAttributes: {
          source: {
            DataType: 'String',
            StringValue: 'rimac.appointments',
          },
          timestamp: {
            DataType: 'String',
            StringValue: new Date().toISOString(),
          },
          messageType: {
            DataType: 'String',
            StringValue: message.eventType as string || 'AppointmentEvent',
          },
        },
      });

      // Send message
      const result = await this.sqsClient.send(command);

      if (!result.MessageId) {
        throw new Error('Failed to get MessageId from SQS send result');
      }

      this.logger.info('Message sent successfully to SQS queue', {
        queueUrl,
        messageId: result.MessageId,
      });

      return result.MessageId;
    } catch (error) {
      this.logger.error('Error sending message to SQS queue', error as Error, {
        queueUrl,
        message,
      });
      throw new Error(`Failed to send SQS message: ${(error as Error).message}`);
    }
  }

  /**
   * Receives messages from the specified SQS queue
   */
  public async receiveMessages(queueUrl: string, maxMessages: number = 1): Promise<unknown[]> {
    try {
      this.logger.debug('Receiving messages from SQS queue', {
        queueUrl,
        maxMessages,
      });

      // Validate inputs
      this.validateQueueUrl(queueUrl);
      
      if (maxMessages < 1 || maxMessages > 10) {
        throw new Error('maxMessages must be between 1 and 10');
      }

      // Create receive message command
      const command = new ReceiveMessageCommand({
        QueueUrl: queueUrl,
        MaxNumberOfMessages: maxMessages,
        WaitTimeSeconds: 20, // Long polling
        MessageAttributeNames: ['All'],
        AttributeNames: ['All'],
      });

      // Receive messages
      const result = await this.sqsClient.send(command);

      const messages = result.Messages || [];

      this.logger.debug('Messages received from SQS queue', {
        queueUrl,
        messageCount: messages.length,
      });

      // Parse message bodies
      return messages.map(message => {
        try {
          return {
            messageId: message.MessageId,
            receiptHandle: message.ReceiptHandle,
            body: message.Body ? JSON.parse(message.Body) : {},
            attributes: message.Attributes || {},
            messageAttributes: message.MessageAttributes || {},
          };
        } catch (parseError) {
          this.logger.warn('Failed to parse SQS message body', {
            messageId: message.MessageId,
            error: (parseError as Error).message,
          });
          return {
            messageId: message.MessageId,
            receiptHandle: message.ReceiptHandle,
            body: message.Body || '',
            attributes: message.Attributes || {},
            messageAttributes: message.MessageAttributes || {},
            parseError: true,
          };
        }
      });
    } catch (error) {
      this.logger.error('Error receiving messages from SQS queue', error as Error, {
        queueUrl,
        maxMessages,
      });
      throw new Error(`Failed to receive SQS messages: ${(error as Error).message}`);
    }
  }

  /**
   * Deletes a message from the specified SQS queue
   */
  public async deleteMessage(queueUrl: string, receiptHandle: string): Promise<void> {
    try {
      this.logger.debug('Deleting message from SQS queue', {
        queueUrl,
        receiptHandle: receiptHandle.substring(0, 50) + '...', // Log partial receipt handle for security
      });

      // Validate inputs
      this.validateQueueUrl(queueUrl);
      
      if (!receiptHandle || typeof receiptHandle !== 'string') {
        throw new Error('Valid receiptHandle is required');
      }

      // Create delete message command
      const command = new DeleteMessageCommand({
        QueueUrl: queueUrl,
        ReceiptHandle: receiptHandle,
      });

      // Delete message
      await this.sqsClient.send(command);

      this.logger.debug('Message deleted successfully from SQS queue', {
        queueUrl,
      });
    } catch (error) {
      this.logger.error('Error deleting message from SQS queue', error as Error, {
        queueUrl,
        receiptHandle: receiptHandle?.substring(0, 50) + '...',
      });
      throw new Error(`Failed to delete SQS message: ${(error as Error).message}`);
    }
  }

  /**
   * Gets queue attributes for monitoring and health checks
   */
  public async getQueueAttributes(queueUrl: string): Promise<Record<string, string>> {
    try {
      this.logger.debug('Getting queue attributes', { queueUrl });

      this.validateQueueUrl(queueUrl);

      const command = new GetQueueAttributesCommand({
        QueueUrl: queueUrl,
        AttributeNames: ['All'],
      });

      const result = await this.sqsClient.send(command);

      this.logger.debug('Queue attributes retrieved successfully', {
        queueUrl,
        attributeCount: Object.keys(result.Attributes || {}).length,
      });

      return result.Attributes || {};
    } catch (error) {
      this.logger.error('Error getting queue attributes', error as Error, { queueUrl });
      throw new Error(`Failed to get queue attributes: ${(error as Error).message}`);
    }
  }

  /**
   * Health check method to verify SQS connectivity
   */
  public async healthCheck(queueUrl: string): Promise<boolean> {
    try {
      await this.getQueueAttributes(queueUrl);
      return true;
    } catch (error) {
      this.logger.error('SQS health check failed', error as Error, { queueUrl });
      return false;
    }
  }

  /**
   * Validates SQS queue URL format
   */
  private validateQueueUrl(queueUrl: string): void {
    if (!queueUrl || typeof queueUrl !== 'string') {
      throw new Error('Valid queueUrl is required');
    }

    // Basic SQS URL format validation
    const sqsUrlPattern = /^https:\/\/sqs\.[a-z0-9-]+\.amazonaws\.com\/\d+\/.+$/;
    if (!sqsUrlPattern.test(queueUrl)) {
      throw new Error('Invalid SQS queue URL format');
    }
  }

  /**
   * Validates message object
   */
  private validateMessage(message: Record<string, unknown>): void {
    if (!message || typeof message !== 'object') {
      throw new Error('Valid message object is required');
    }

    // Check message size (SQS limit is 256KB)
    const messageSize = JSON.stringify(message).length;
    if (messageSize > 262144) { // 256KB in bytes
      throw new Error('Message size exceeds SQS limit of 256KB');
    }
  }
}
