# Multi-stage Dockerfile for Rimac Medical Appointments Backend
# Optimized for both development and production environments

# Stage 1: Base image with Node.js
FROM node:18-alpine AS base

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    curl \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Stage 2: Development dependencies
FROM base AS development

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY . .

# Expose port for local development
EXPOSE 3000

# Default command for development
CMD ["npm", "run", "dev"]

# Stage 3: Build stage
FROM base AS build

# Install all dependencies
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Stage 4: Production dependencies
FROM base AS production-deps

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Stage 5: Production image
FROM node:18-alpine AS production

# Set NODE_ENV
ENV NODE_ENV=production

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S rimac -u 1001

# Set working directory
WORKDIR /app

# Copy production dependencies
COPY --from=production-deps --chown=rimac:nodejs /app/node_modules ./node_modules

# Copy built application
COPY --from=build --chown=rimac:nodejs /app/dist ./dist
COPY --from=build --chown=rimac:nodejs /app/package*.json ./

# Switch to non-root user
USER rimac

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Expose port
EXPOSE 3000

# Start the application
CMD ["node", "dist/index.js"]

# Stage 6: Testing image
FROM development AS testing

# Copy test files
COPY src/tests ./src/tests

# Run tests
RUN npm test

# Stage 7: Local development with hot reload
FROM development AS local

# Install serverless framework globally
RUN npm install -g serverless@3.38.0

# Install additional development tools
RUN npm install -g nodemon ts-node

# Create volume mount points
VOLUME ["/app/src", "/app/node_modules"]

# Expose ports for serverless offline
EXPOSE 3000 3001

# Command for local development with hot reload
CMD ["npm", "run", "dev"]
