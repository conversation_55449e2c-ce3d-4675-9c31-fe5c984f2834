service: rimac-medical-appointments

frameworkVersion: '3'

plugins:
  - serverless-plugin-typescript
  - serverless-offline
  - serverless-dotenv-plugin

provider:
  name: aws
  runtime: nodejs18.x
  region: ${env:AWS_REGION, 'us-east-1'}
  stage: ${env:STAGE, 'dev'}
  memorySize: 512
  timeout: 30
  environment:
    STAGE: ${self:provider.stage}
    AWS_REGION: ${self:provider.region}
    DYNAMODB_APPOINTMENTS_TABLE: ${self:custom.appointmentsTableName}
    SNS_TOPIC_ARN: !Ref AppointmentsTopic
    SQS_PE_QUEUE_URL: !Ref AppointmentsPEQueue
    SQS_CL_QUEUE_URL: !Ref AppointmentsCLQueue
    SQS_CONFIRMATION_QUEUE_URL: !Ref AppointmentsConfirmationQueue
    EVENTBRIDGE_BUS_NAME: ${self:custom.eventBridgeBusName}
    DB_HOST: ${env:DB_HOST}
    DB_PORT: ${env:DB_PORT}
    DB_NAME: ${env:DB_NAME}
    DB_USER: ${env:DB_USER}
    DB_PASSWORD: ${env:DB_PASSWORD}
    DB_CONNECTION_LIMIT: ${env:DB_CONNECTION_LIMIT, '10'}
    LOG_LEVEL: ${env:LOG_LEVEL, 'info'}
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - dynamodb:Query
            - dynamodb:Scan
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
          Resource:
            - !GetAtt AppointmentsTable.Arn
            - !Sub "${AppointmentsTable.Arn}/index/*"
        - Effect: Allow
          Action:
            - sns:Publish
          Resource: !Ref AppointmentsTopic
        - Effect: Allow
          Action:
            - sqs:SendMessage
            - sqs:ReceiveMessage
            - sqs:DeleteMessage
            - sqs:GetQueueAttributes
          Resource:
            - !GetAtt AppointmentsPEQueue.Arn
            - !GetAtt AppointmentsCLQueue.Arn
            - !GetAtt AppointmentsConfirmationQueue.Arn
        - Effect: Allow
          Action:
            - events:PutEvents
          Resource: !GetAtt AppointmentsEventBridge.Arn
        - Effect: Allow
          Action:
            - rds:DescribeDBInstances
            - rds:DescribeDBClusters
          Resource: "*"
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource: "*"

custom:
  appointmentsTableName: rimac-appointments-${self:provider.stage}
  eventBridgeBusName: rimac-appointments-bus-${self:provider.stage}
  dotenv:
    path: .env

functions:
  # Main appointment handler
  appointment:
    handler: src/handlers/appointment.handler
    events:
      - http:
          path: /appointments
          method: post
          cors: true
      - http:
          path: /appointments/{insuredId}
          method: get
          cors: true
      - sqs:
          arn: !GetAtt AppointmentsConfirmationQueue.Arn
          batchSize: 1

  # Country-specific handlers
  appointmentPE:
    handler: src/handlers/appointmentPE.handler
    events:
      - sqs:
          arn: !GetAtt AppointmentsPEQueue.Arn
          batchSize: 1

  appointmentCL:
    handler: src/handlers/appointmentCL.handler
    events:
      - sqs:
          arn: !GetAtt AppointmentsCLQueue.Arn
          batchSize: 1

  # Swagger documentation
  docs:
    handler: src/handlers/docs.handler
    events:
      - http:
          path: /docs
          method: get
          cors: true
      - http:
          path: /docs/{proxy+}
          method: get
          cors: true

resources:
  Resources:
    # DynamoDB Table
    AppointmentsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.appointmentsTableName}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: appointmentId
            AttributeType: S
          - AttributeName: insuredId
            AttributeType: S
        KeySchema:
          - AttributeName: appointmentId
            KeyType: HASH
        GlobalSecondaryIndexes:
          - IndexName: InsuredIdIndex
            KeySchema:
              - AttributeName: insuredId
                KeyType: HASH
            Projection:
              ProjectionType: ALL

    # SNS Topic
    AppointmentsTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: rimac-appointments-topic-${self:provider.stage}
        DisplayName: Rimac Medical Appointments Topic

    # SNS Subscriptions with filters
    AppointmentsPESubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Protocol: sqs
        TopicArn: !Ref AppointmentsTopic
        Endpoint: !GetAtt AppointmentsPEQueue.Arn
        FilterPolicy:
          countryISO:
            - "PE"

    AppointmentsCLSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Protocol: sqs
        TopicArn: !Ref AppointmentsTopic
        Endpoint: !GetAtt AppointmentsCLQueue.Arn
        FilterPolicy:
          countryISO:
            - "CL"

    # SQS Queues
    AppointmentsPEQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: rimac-appointments-pe-${self:provider.stage}
        VisibilityTimeoutSeconds: 180
        MessageRetentionPeriod: 1209600
        RedrivePolicy:
          deadLetterTargetArn: !GetAtt AppointmentsPEDLQ.Arn
          maxReceiveCount: 3

    AppointmentsCLQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: rimac-appointments-cl-${self:provider.stage}
        VisibilityTimeoutSeconds: 180
        MessageRetentionPeriod: 1209600
        RedrivePolicy:
          deadLetterTargetArn: !GetAtt AppointmentsCLDLQ.Arn
          maxReceiveCount: 3

    AppointmentsConfirmationQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: rimac-appointments-confirmation-${self:provider.stage}
        VisibilityTimeoutSeconds: 180
        MessageRetentionPeriod: 1209600

    # Dead Letter Queues
    AppointmentsPEDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: rimac-appointments-pe-dlq-${self:provider.stage}

    AppointmentsCLDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: rimac-appointments-cl-dlq-${self:provider.stage}

    # SQS Queue Policies
    AppointmentsPEQueuePolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        Queues:
          - !Ref AppointmentsPEQueue
        PolicyDocument:
          Statement:
            - Effect: Allow
              Principal:
                Service: sns.amazonaws.com
              Action: sqs:SendMessage
              Resource: !GetAtt AppointmentsPEQueue.Arn
              Condition:
                ArnEquals:
                  aws:SourceArn: !Ref AppointmentsTopic

    AppointmentsCLQueuePolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        Queues:
          - !Ref AppointmentsCLQueue
        PolicyDocument:
          Statement:
            - Effect: Allow
              Principal:
                Service: sns.amazonaws.com
              Action: sqs:SendMessage
              Resource: !GetAtt AppointmentsCLQueue.Arn
              Condition:
                ArnEquals:
                  aws:SourceArn: !Ref AppointmentsTopic

    # EventBridge Custom Bus
    AppointmentsEventBridge:
      Type: AWS::Events::EventBus
      Properties:
        Name: ${self:custom.eventBridgeBusName}

    # EventBridge Rule
    AppointmentsEventRule:
      Type: AWS::Events::Rule
      Properties:
        EventBusName: !Ref AppointmentsEventBridge
        EventPattern:
          source:
            - "rimac.appointments"
          detail-type:
            - "Appointment Processed"
        State: ENABLED
        Targets:
          - Arn: !GetAtt AppointmentsConfirmationQueue.Arn
            Id: "AppointmentConfirmationTarget"
            SqsParameters:
              MessageGroupId: "appointment-confirmations"

    # EventBridge to SQS Permission
    EventBridgeToSQSPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        Queues:
          - !Ref AppointmentsConfirmationQueue
        PolicyDocument:
          Statement:
            - Effect: Allow
              Principal:
                Service: events.amazonaws.com
              Action: sqs:SendMessage
              Resource: !GetAtt AppointmentsConfirmationQueue.Arn

    # RDS MySQL Instance
    AppointmentsRDSSubnetGroup:
      Type: AWS::RDS::DBSubnetGroup
      Properties:
        DBSubnetGroupDescription: Subnet group for Rimac Appointments RDS
        SubnetIds:
          - !Ref AppointmentsPrivateSubnet1
          - !Ref AppointmentsPrivateSubnet2
        Tags:
          - Key: Name
            Value: rimac-appointments-subnet-group-${self:provider.stage}

    AppointmentsRDSSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Security group for Rimac Appointments RDS
        VpcId: !Ref AppointmentsVPC
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 3306
            ToPort: 3306
            SourceSecurityGroupId: !Ref AppointmentsLambdaSecurityGroup
        Tags:
          - Key: Name
            Value: rimac-appointments-rds-sg-${self:provider.stage}

    AppointmentsRDS:
      Type: AWS::RDS::DBInstance
      Properties:
        DBInstanceIdentifier: rimac-appointments-${self:provider.stage}
        DBInstanceClass: db.t3.micro
        Engine: mysql
        EngineVersion: '8.0'
        MasterUsername: ${env:DB_USER}
        MasterUserPassword: ${env:DB_PASSWORD}
        AllocatedStorage: 20
        StorageType: gp2
        DBName: ${env:DB_NAME}
        DBSubnetGroupName: !Ref AppointmentsRDSSubnetGroup
        VPCSecurityGroups:
          - !Ref AppointmentsRDSSecurityGroup
        BackupRetentionPeriod: 7
        MultiAZ: false
        PubliclyAccessible: false
        StorageEncrypted: true
        Tags:
          - Key: Name
            Value: rimac-appointments-rds-${self:provider.stage}

    # VPC Configuration for RDS
    AppointmentsVPC:
      Type: AWS::EC2::VPC
      Properties:
        CidrBlock: 10.0.0.0/16
        EnableDnsHostnames: true
        EnableDnsSupport: true
        Tags:
          - Key: Name
            Value: rimac-appointments-vpc-${self:provider.stage}

    AppointmentsPrivateSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref AppointmentsVPC
        CidrBlock: ********/24
        AvailabilityZone: !Select [0, !GetAZs '']
        Tags:
          - Key: Name
            Value: rimac-appointments-private-subnet-1-${self:provider.stage}

    AppointmentsPrivateSubnet2:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref AppointmentsVPC
        CidrBlock: ********/24
        AvailabilityZone: !Select [1, !GetAZs '']
        Tags:
          - Key: Name
            Value: rimac-appointments-private-subnet-2-${self:provider.stage}

    AppointmentsLambdaSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Security group for Lambda functions
        VpcId: !Ref AppointmentsVPC
        SecurityGroupEgress:
          - IpProtocol: tcp
            FromPort: 3306
            ToPort: 3306
            DestinationSecurityGroupId: !Ref AppointmentsRDSSecurityGroup
        Tags:
          - Key: Name
            Value: rimac-appointments-lambda-sg-${self:provider.stage}

  Outputs:
    AppointmentsTableName:
      Description: DynamoDB table name for appointments
      Value: !Ref AppointmentsTable
      Export:
        Name: ${self:provider.stage}-AppointmentsTableName

    AppointmentsTopicArn:
      Description: SNS Topic ARN for appointments
      Value: !Ref AppointmentsTopic
      Export:
        Name: ${self:provider.stage}-AppointmentsTopicArn

    AppointmentsRDSEndpoint:
      Description: RDS MySQL endpoint
      Value: !GetAtt AppointmentsRDS.Endpoint.Address
      Export:
        Name: ${self:provider.stage}-AppointmentsRDSEndpoint
