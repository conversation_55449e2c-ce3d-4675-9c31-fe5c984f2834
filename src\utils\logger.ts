/**
 * Logger Implementation using <PERSON>
 * Provides structured logging for the application
 */

import winston from 'winston';
import { ILogger } from '@/entities/interfaces';

export class Logger implements ILogger {
  private logger: winston.Logger;

  constructor() {
    const logLevel = process.env.LOG_LEVEL || 'info';
    const isProduction = process.env.NODE_ENV === 'production';

    // Define log format
    const logFormat = winston.format.combine(
      winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss.SSS',
      }),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
        const logEntry = {
          timestamp,
          level,
          message,
          ...(stack && { stack }),
          ...(Object.keys(meta).length > 0 && { meta }),
        };
        return JSON.stringify(logEntry);
      })
    );

    // Configure transports
    const transports: winston.transport[] = [];

    if (isProduction) {
      // In production, log to CloudWatch (stdout/stderr)
      transports.push(
        new winston.transports.Console({
          level: logLevel,
          format: logFormat,
        })
      );
    } else {
      // In development, log to console with colors
      transports.push(
        new winston.transports.Console({
          level: logLevel,
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.timestamp({
              format: 'HH:mm:ss.SSS',
            }),
            winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
              let logMessage = `${timestamp} [${level}]: ${message}`;
              
              if (Object.keys(meta).length > 0) {
                logMessage += ` ${JSON.stringify(meta, null, 2)}`;
              }
              
              if (stack) {
                logMessage += `\n${stack}`;
              }
              
              return logMessage;
            })
          ),
        })
      );
    }

    // Create logger instance
    this.logger = winston.createLogger({
      level: logLevel,
      format: logFormat,
      transports,
      exitOnError: false,
    });

    // Handle uncaught exceptions and unhandled rejections
    this.logger.exceptions.handle(
      new winston.transports.Console({
        format: logFormat,
      })
    );

    this.logger.rejections.handle(
      new winston.transports.Console({
        format: logFormat,
      })
    );
  }

  /**
   * Log info level message
   */
  public info(message: string, meta?: Record<string, unknown>): void {
    this.logger.info(message, meta);
  }

  /**
   * Log error level message
   */
  public error(message: string, error?: Error, meta?: Record<string, unknown>): void {
    const errorMeta = {
      ...meta,
      ...(error && {
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
      }),
    };

    this.logger.error(message, errorMeta);
  }

  /**
   * Log warning level message
   */
  public warn(message: string, meta?: Record<string, unknown>): void {
    this.logger.warn(message, meta);
  }

  /**
   * Log debug level message
   */
  public debug(message: string, meta?: Record<string, unknown>): void {
    this.logger.debug(message, meta);
  }

  /**
   * Create a child logger with additional context
   */
  public child(context: Record<string, unknown>): Logger {
    const childLogger = new Logger();
    childLogger.logger = this.logger.child(context);
    return childLogger;
  }

  /**
   * Get the underlying Winston logger instance
   */
  public getWinstonLogger(): winston.Logger {
    return this.logger;
  }
}

/**
 * Create a singleton logger instance
 */
let loggerInstance: Logger | null = null;

export function getLogger(): Logger {
  if (!loggerInstance) {
    loggerInstance = new Logger();
  }
  return loggerInstance;
}

/**
 * Create a logger with specific context
 */
export function createLogger(context: Record<string, unknown>): Logger {
  return getLogger().child(context);
}

/**
 * Lambda request logger middleware
 */
export function createRequestLogger(requestId: string, functionName: string): Logger {
  return createLogger({
    requestId,
    functionName,
    service: 'rimac-medical-appointments',
  });
}
