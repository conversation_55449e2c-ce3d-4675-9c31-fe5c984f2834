/**
 * Unit Tests for AppointmentController
 * Tests the HTTP request handling and response formatting
 */

import { AppointmentController } from '@/controllers/AppointmentController';
import { IAppointmentService, IAPIGatewayEvent, ILogger } from '@/entities/interfaces';
import { CreateAppointmentResponseDTO, ListAppointmentsResponseDTO } from '@/entities/DTOs';

// Mock dependencies
const mockAppointmentService: jest.Mocked<IAppointmentService> = {
  createAppointment: jest.fn(),
  getAppointmentsByInsuredId: jest.fn(),
  updateAppointmentStatus: jest.fn(),
  processAppointmentConfirmation: jest.fn(),
};

const mockLogger: jest.Mocked<ILogger> = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};

// Mock API Gateway event
const createMockEvent = (overrides: Partial<IAPIGatewayEvent> = {}): IAPIGatewayEvent => ({
  httpMethod: 'POST',
  path: '/appointments',
  pathParameters: null,
  queryStringParameters: null,
  headers: {
    'Content-Type': 'application/json',
  },
  body: null,
  requestContext: {
    requestId: 'test-request-id',
    stage: 'test',
    httpMethod: 'POST',
    path: '/appointments',
  },
  ...overrides,
});

describe('AppointmentController', () => {
  let appointmentController: AppointmentController;

  beforeEach(() => {
    jest.clearAllMocks();
    appointmentController = new AppointmentController(mockAppointmentService, mockLogger);
  });

  describe('createAppointment', () => {
    const validRequestBody = JSON.stringify({
      insuredId: '00012',
      scheduleId: 100,
      countryISO: 'PE',
    });

    const mockServiceResponse: CreateAppointmentResponseDTO = {
      message: 'Appointment request received and is pending',
      appointmentId: 'test-uuid',
      status: 'pending',
      timestamp: '2024-01-15T10:30:00.000Z',
    };

    it('should create appointment successfully', async () => {
      // Arrange
      const event = createMockEvent({ body: validRequestBody });
      mockAppointmentService.createAppointment.mockResolvedValue(mockServiceResponse);

      // Act
      const result = await appointmentController.createAppointment(event);

      // Assert
      expect(result.statusCode).toBe(201);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockServiceResponse,
        timestamp: expect.any(String),
        requestId: 'test-request-id',
      });

      expect(mockAppointmentService.createAppointment).toHaveBeenCalledWith({
        insuredId: '00012',
        scheduleId: 100,
        countryISO: 'PE',
      });

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Processing create appointment request',
        expect.any(Object)
      );
    });

    it('should return 400 for missing request body', async () => {
      // Arrange
      const event = createMockEvent({ body: null });

      // Act
      const result = await appointmentController.createAppointment(event);

      // Assert
      expect(result.statusCode).toBe(400);
      expect(JSON.parse(result.body)).toEqual({
        success: false,
        error: {
          code: 'MISSING_BODY',
          message: 'Request body is required',
        },
        timestamp: expect.any(String),
        requestId: 'test-request-id',
      });

      expect(mockAppointmentService.createAppointment).not.toHaveBeenCalled();
    });

    it('should return 400 for invalid JSON', async () => {
      // Arrange
      const event = createMockEvent({ body: 'invalid-json' });

      // Act
      const result = await appointmentController.createAppointment(event);

      // Assert
      expect(result.statusCode).toBe(400);
      expect(JSON.parse(result.body)).toEqual({
        success: false,
        error: {
          code: 'INVALID_JSON',
          message: 'Invalid JSON in request body',
        },
        timestamp: expect.any(String),
        requestId: 'test-request-id',
      });
    });

    it('should return 400 for missing insuredId', async () => {
      // Arrange
      const invalidBody = JSON.stringify({
        scheduleId: 100,
        countryISO: 'PE',
      });
      const event = createMockEvent({ body: invalidBody });

      // Act
      const result = await appointmentController.createAppointment(event);

      // Assert
      expect(result.statusCode).toBe(400);
      expect(JSON.parse(result.body)).toEqual({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'insuredId is required and must be a non-empty string',
        },
        timestamp: expect.any(String),
        requestId: 'test-request-id',
      });
    });

    it('should return 400 for invalid scheduleId', async () => {
      // Arrange
      const invalidBody = JSON.stringify({
        insuredId: '00012',
        scheduleId: 0,
        countryISO: 'PE',
      });
      const event = createMockEvent({ body: invalidBody });

      // Act
      const result = await appointmentController.createAppointment(event);

      // Assert
      expect(result.statusCode).toBe(400);
      expect(JSON.parse(result.body).error.message).toContain('scheduleId is required');
    });

    it('should return 400 for invalid countryISO', async () => {
      // Arrange
      const invalidBody = JSON.stringify({
        insuredId: '00012',
        scheduleId: 100,
        countryISO: 'US',
      });
      const event = createMockEvent({ body: invalidBody });

      // Act
      const result = await appointmentController.createAppointment(event);

      // Assert
      expect(result.statusCode).toBe(400);
      expect(JSON.parse(result.body).error.message).toContain('countryISO is required');
    });

    it('should return 500 for service error', async () => {
      // Arrange
      const event = createMockEvent({ body: validRequestBody });
      const serviceError = new Error('Service unavailable');
      mockAppointmentService.createAppointment.mockRejectedValue(serviceError);

      // Act
      const result = await appointmentController.createAppointment(event);

      // Assert
      expect(result.statusCode).toBe(500);
      expect(JSON.parse(result.body)).toEqual({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An internal error occurred while creating the appointment',
        },
        timestamp: expect.any(String),
        requestId: 'test-request-id',
      });

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error creating appointment',
        serviceError,
        { requestId: 'test-request-id' }
      );
    });
  });

  describe('getAppointmentsByInsuredId', () => {
    const mockServiceResponse: ListAppointmentsResponseDTO = {
      appointments: [
        {
          appointmentId: 'test-uuid',
          scheduleId: 100,
          countryISO: 'PE',
          status: 'completed',
          createdAt: '2024-01-15T10:30:00.000Z',
          updatedAt: '2024-01-15T10:35:00.000Z',
        },
      ],
      count: 1,
      insuredId: '00012',
    };

    it('should retrieve appointments successfully', async () => {
      // Arrange
      const event = createMockEvent({
        httpMethod: 'GET',
        path: '/appointments/00012',
        pathParameters: { insuredId: '00012' },
      });
      mockAppointmentService.getAppointmentsByInsuredId.mockResolvedValue(mockServiceResponse);

      // Act
      const result = await appointmentController.getAppointmentsByInsuredId(event);

      // Assert
      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockServiceResponse,
        timestamp: expect.any(String),
        requestId: 'test-request-id',
      });

      expect(mockAppointmentService.getAppointmentsByInsuredId).toHaveBeenCalledWith('00012');
    });

    it('should return 400 for missing insuredId parameter', async () => {
      // Arrange
      const event = createMockEvent({
        httpMethod: 'GET',
        path: '/appointments/',
        pathParameters: null,
      });

      // Act
      const result = await appointmentController.getAppointmentsByInsuredId(event);

      // Assert
      expect(result.statusCode).toBe(400);
      expect(JSON.parse(result.body)).toEqual({
        success: false,
        error: {
          code: 'MISSING_PARAMETER',
          message: 'insuredId parameter is required',
        },
        timestamp: expect.any(String),
        requestId: 'test-request-id',
      });
    });

    it('should return 400 for empty insuredId parameter', async () => {
      // Arrange
      const event = createMockEvent({
        httpMethod: 'GET',
        path: '/appointments/ ',
        pathParameters: { insuredId: ' ' },
      });

      // Act
      const result = await appointmentController.getAppointmentsByInsuredId(event);

      // Assert
      expect(result.statusCode).toBe(400);
      expect(JSON.parse(result.body).error.message).toContain('insuredId must be a non-empty string');
    });

    it('should return 500 for service error', async () => {
      // Arrange
      const event = createMockEvent({
        httpMethod: 'GET',
        path: '/appointments/00012',
        pathParameters: { insuredId: '00012' },
      });
      const serviceError = new Error('Database connection failed');
      mockAppointmentService.getAppointmentsByInsuredId.mockRejectedValue(serviceError);

      // Act
      const result = await appointmentController.getAppointmentsByInsuredId(event);

      // Assert
      expect(result.statusCode).toBe(500);
      expect(JSON.parse(result.body)).toEqual({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An internal error occurred while retrieving appointments',
        },
        timestamp: expect.any(String),
        requestId: 'test-request-id',
      });
    });
  });

  describe('processConfirmation', () => {
    it('should process confirmation successfully', async () => {
      // Arrange
      mockAppointmentService.processAppointmentConfirmation.mockResolvedValue();

      // Act
      await appointmentController.processConfirmation('test-appointment-id', true, 'test-request-id');

      // Assert
      expect(mockAppointmentService.processAppointmentConfirmation).toHaveBeenCalledWith(
        'test-appointment-id',
        true
      );

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Processing appointment confirmation',
        expect.objectContaining({
          requestId: 'test-request-id',
          appointmentId: 'test-appointment-id',
          success: true,
        })
      );
    });

    it('should handle service error', async () => {
      // Arrange
      const serviceError = new Error('Confirmation processing failed');
      mockAppointmentService.processAppointmentConfirmation.mockRejectedValue(serviceError);

      // Act & Assert
      await expect(
        appointmentController.processConfirmation('test-appointment-id', false, 'test-request-id')
      ).rejects.toThrow('Confirmation processing failed');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error processing appointment confirmation',
        serviceError,
        expect.objectContaining({
          requestId: 'test-request-id',
          appointmentId: 'test-appointment-id',
          success: false,
        })
      );
    });
  });

  describe('CORS headers', () => {
    it('should include CORS headers in success response', async () => {
      // Arrange
      const event = createMockEvent({ body: JSON.stringify({
        insuredId: '00012',
        scheduleId: 100,
        countryISO: 'PE',
      })});
      mockAppointmentService.createAppointment.mockResolvedValue({
        message: 'Success',
        appointmentId: 'test-uuid',
        status: 'pending',
        timestamp: '2024-01-15T10:30:00.000Z',
      });

      // Act
      const result = await appointmentController.createAppointment(event);

      // Assert
      expect(result.headers).toEqual(
        expect.objectContaining({
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
          'Access-Control-Allow-Methods': 'GET,POST,OPTIONS',
        })
      );
    });

    it('should include CORS headers in error response', async () => {
      // Arrange
      const event = createMockEvent({ body: null });

      // Act
      const result = await appointmentController.createAppointment(event);

      // Assert
      expect(result.headers).toEqual(
        expect.objectContaining({
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
          'Access-Control-Allow-Methods': 'GET,POST,OPTIONS',
        })
      );
    });
  });
});
